import { createMediaComposition } from './video-poster-generation/index.mjs';
import { join } from 'node:path';
import { tmpdir } from 'node:os';

// Test multiple video elements with different border configurations
const payload = [
    {
        url: "https://httpbin.org/image/jpeg",
        width: 360,
        height: 200,
        x: 0,
        y: 0,
        type: "photo"
    },
    {
        url: "https://httpbin.org/image/png",
        width: 200,
        height: 150,
        x: 50,
        y: 300,
        type: "video",
        border_radius: 20,
        border_size: 12,
        border_color: "#ff0000"  // Red border
    },
    {
        url: "https://httpbin.org/image/jpeg",
        width: 150,
        height: 100,
        x: 150,
        y: 100,
        type: "video",
        border_radius: 10,
        border_size: 6,
        border_color: "#0000ff"  // Blue border
    }
];

async function testMultipleBorders() {
    console.log('Testing multiple video elements with different border configurations...');
    console.log('Expected:');
    console.log('- Photo as base layer (no border)');
    console.log('- Video 1: RED border (#ff0000) with 12px width and 20px radius');
    console.log('- Video 2: BLUE border (#0000ff) with 6px width and 10px radius');

    const outputPath = join(tmpdir(), 'test-multiple-borders.mp4');

    try {
        console.log('\nCreating media composition...');
        const metadata = await createMediaComposition(
            payload,
            360,  // frame_width
            720,  // frame_height
            outputPath,
            'test-multiple-borders',
            'https://httpbin.org/anything'
        );

        console.log('\n✅ Success! Video created at:', outputPath);
        console.log('📊 Video Metadata:', metadata);

        console.log('\n🎯 The video should show:');
        console.log('1. Base photo layer');
        console.log('2. Video with RED border (12px width) at position (50, 300)');
        console.log('3. Video with BLUE border (6px width) at position (150, 100)');
        console.log('4. Border radius noted but not visually rounded (as expected)');

    } catch (error) {
        console.error('\n❌ Error:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Run the test
testMultipleBorders();
