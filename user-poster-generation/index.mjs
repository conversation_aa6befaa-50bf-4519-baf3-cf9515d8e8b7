import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import fs from 'fs';
import { join } from 'path';
import { tmpdir } from "os";
import puppeteer from 'puppeteer-core';
import chromium from '@sparticuz/chromium';

function delay(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}

const _folderName = 'production/user-campaign-posters';

export const handler = async (event) => {

    const { creative_id, user_id, frame_id, bearer_token, locked } = event;

    if (!creative_id || !user_id || !bearer_token) {
        return {
            statusCode: 400,
            body: JSON.stringify({ message: 'Missing required parameters: creative_id, user_id, bearer_token' }),
        };
    }

    console.log('Generating Poster for creative_id:', creative_id, 'user_id:', user_id, 'frame_id:', frame_id);

    let browser;

    try {
        // Launch the browser
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-web-security', ...chromium.args],
            executablePath: await chromium.executablePath(),
        });

        const page = await browser.newPage();
        await page.setViewport({ width: 600, height: 750, deviceScaleFactor: 1 });
        await page.setExtraHTTPHeaders({ Authorization: 'Bearer ' + bearer_token });

        let url = `https://pp.thecircleapp.in/#/poster_page?creative_id=${creative_id}&user_id=${user_id}`;
        if (locked !== undefined) {
            url += `&locked=${locked}`;
        }
        if (frame_id) {
            url += `&frame_id=${frame_id}`;
        }

        await page.goto(url, { waitUntil: 'networkidle0', timeout: 30000 });
        await page.waitForNetworkIdle({ idleTime: 2000, timeout: 30000 });
        await delay(2000); // Give some time for rendering

        // Take the screenshot
        const screenshotPath = join(tmpdir(), `poster_page_${creative_id}_${user_id}.png`);
        await page.screenshot({ path: screenshotPath, fullPage: true });

        // Upload to S3
        const bucketName = 'circle-app-photos';
        // image files are cached, so we need to add a timestamp to the file name to
        // avoid caching while regenerating the poster for the same user and same event
        const currentTimeStamp = new Date().getTime();
        const outputFileName = frame_id
            ? `poster-${creative_id}-${user_id}-${frame_id}-${currentTimeStamp}.png`
            : `poster-${creative_id}-${user_id}-${currentTimeStamp}.png`;
        await uploadPosterToS3(screenshotPath, bucketName, outputFileName);

        // return `https://l-cdn.praja.buzz/${_folderName}/${outputFileName}`; //uncomment for testing
        return {
            statusCode: 200,
            body: JSON.stringify({
                poster_url: `https://a-cdn.thecircleapp.in/${_folderName}/${outputFileName}`,
                message: 'Poster generated successfully',
            }),
        };

    } catch (err) {
        console.error('Error in generating poster:', err);
        if (browser) {
            await browser.close();
        }
        return {
            statusCode: 500,
            body: JSON.stringify({ message: 'Failed to generate poster' }),
        };
    } finally {
        // Ensure browser is closed in case of any error or successful completion [safe side]
        if (browser) {
            await browser.close();
        }
    }
};

export const uploadPosterToS3 = async (filePath, bucketName, fileName) => {
    const s3 = new S3Client({ region: 'ap-south-1' });
    const uploadParams = {
        Bucket: bucketName,
        Key: `${_folderName}/${fileName}`,
        Body: fs.createReadStream(filePath),
    };

    try {
        await s3.send(new PutObjectCommand(uploadParams));
    } catch (err) {
        console.error('Error uploading to S3:', err);
        throw new Error(`Failed to upload poster: ${err.message}`);
    }
};
