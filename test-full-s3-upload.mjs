import { handler } from './video-poster-generation/index.mjs';

// Test the complete workflow including S3 upload
const testEvent = {
    payload: [
        {
            url: "https://httpbin.org/image/jpeg",
            width: 360,
            height: 200,
            x: 0,
            y: 0,
            type: "photo"
        },
        {
            url: "https://httpbin.org/image/png", 
            width: 200,
            height: 150,
            x: 50,
            y: 300,
            type: "video",
            border_radius: 20,
            border_size: 12,
            border_color: "#ff0000"  // Red border
        },
        {
            url: "https://httpbin.org/image/jpeg",
            width: 150,
            height: 100,
            x: 150,
            y: 100,
            type: "video", 
            border_radius: 10,
            border_size: 6,
            border_color: "#0000ff"  // Blue border
        }
    ],
    frame_height: 720,
    frame_width: 360,
    job_id: "test-s3-upload-123",
    callback_url: "https://httpbin.org/anything"
};

async function testFullS3Upload() {
    console.log('🚀 Testing COMPLETE workflow including S3 upload...');
    console.log('This will:');
    console.log('1. Create video composition with borders');
    console.log('2. Upload video to S3 bucket');
    console.log('3. Generate and upload thumbnail');
    console.log('4. Return S3 URLs in response');
    console.log('5. Send callback to webhook');
    
    console.log('\nTest payload:', JSON.stringify(testEvent, null, 2));

    try {
        console.log('\n⏳ Calling full handler (this may take a while for S3 upload)...');
        const result = await handler(testEvent);
        
        console.log('\n✅ SUCCESS! Full workflow completed!');
        console.log('Result:', JSON.stringify(result, null, 2));

        // Parse the response body to check S3 URLs
        const responseBody = JSON.parse(result.body);

        console.log('\n📊 Generated Content:');
        console.log(`🎥 Video URL: ${responseBody.video_url}`);
        console.log(`🖼️  Thumbnail URL: ${responseBody.thumbnail_url}`);
        console.log(`📏 Dimensions: ${responseBody.width}x${responseBody.height}`);
        console.log(`⏱️  Duration: ${responseBody.duration}s`);
        console.log(`📊 Bitrate: ${responseBody.bitrate} bps`);

        console.log('\n🎯 Expected in the video:');
        console.log('- Base photo layer');
        console.log('- Video with RED border (12px) at position (50, 300)');
        console.log('- Video with BLUE border (6px) at position (150, 100)');
        console.log('- Both videos uploaded to S3 and accessible via URLs');

        // Verify S3 URLs are properly formatted
        if (responseBody.video_url && responseBody.video_url.includes('s3.ap-south-1.amazonaws.com')) {
            console.log('\n✅ Video successfully uploaded to S3!');
        } else {
            console.log('\n❌ Video S3 URL seems incorrect');
        }

        if (responseBody.thumbnail_url && responseBody.thumbnail_url.includes('s3.ap-south-1.amazonaws.com')) {
            console.log('✅ Thumbnail successfully uploaded to S3!');
        } else {
            console.log('❌ Thumbnail S3 URL seems incorrect');
        }

    } catch (error) {
        console.error('\n❌ Error during full workflow:');
        console.error('- Message:', error?.message || 'undefined');
        console.error('- Stack:', error?.stack || 'undefined');
        
        // Check if it's an S3 permissions issue
        if (error?.message?.includes('S3') || error?.message?.includes('AWS')) {
            console.error('\n💡 This might be an AWS credentials or S3 permissions issue.');
            console.error('Make sure AWS credentials are configured for S3 access.');
        }
    }
}

// Run the test
testFullS3Upload();
