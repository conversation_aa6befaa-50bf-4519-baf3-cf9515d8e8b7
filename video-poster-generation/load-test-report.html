
<!DOCTYPE html>
<html>
<head>
    <title>Video Poster Generation Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f9f9f9; border-radius: 3px; }
        .recommendation { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f0f8ff; }
        .high-priority { border-left-color: #d32f2f; background: #ffebee; }
        .medium-priority { border-left-color: #f57c00; background: #fff3e0; }
        .low-priority { border-left-color: #388e3c; background: #e8f5e8; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Video Poster Generation Load Test Report</h1>
        <p>Generated: 2025-05-28T06:00:14.959Z</p>
        <p>Test Suite: AWS Lambda Load Test</p>
    </div>

    <div class="section">
        <h2>Performance Summary</h2>
        <div class="metric">
            <strong>Average Processing Time:</strong> 3.72s
        </div>
        <div class="metric">
            <strong>Success Rate:</strong> 100.00%
        </div>
        <div class="metric">
            <strong>Peak Memory Usage:</strong> N/AMB
        </div>
        <div class="metric">
            <strong>Optimal Concurrency:</strong> N/A
        </div>
    </div>

    <div class="section">
        <h2>Detailed Test Results</h2>
        <table>
            <tr>
                <th>Test</th>
                <th>Success Rate</th>
                <th>Avg Time (s)</th>
                <th>Throughput</th>
                <th>Memory (MB)</th>
            </tr>
            
                <tr>
                    <td>concurrency-1</td>
                    <td>100.00%</td>
                    <td>5.95</td>
                    <td>0.17</td>
                    <td>N/A</td>
                </tr>
            
                <tr>
                    <td>concurrency-3</td>
                    <td>100.00%</td>
                    <td>3.77</td>
                    <td>0.69</td>
                    <td>N/A</td>
                </tr>
            
                <tr>
                    <td>concurrency-5</td>
                    <td>100.00%</td>
                    <td>2.97</td>
                    <td>1.25</td>
                    <td>N/A</td>
                </tr>
            
                <tr>
                    <td>concurrency-8</td>
                    <td>100.00%</td>
                    <td>3.17</td>
                    <td>1.72</td>
                    <td>N/A</td>
                </tr>
            
                <tr>
                    <td>concurrency-10</td>
                    <td>100.00%</td>
                    <td>2.72</td>
                    <td>2.73</td>
                    <td>N/A</td>
                </tr>
            
        </table>
    </div>

    <div class="section">
        <h2>Recommendations</h2>
        
            <div class="recommendation low-priority">
                <strong>Performance (Low Priority):</strong> Excellent performance detected<br>
                <em>Recommendation:</em> Consider reducing Lambda memory allocation to optimize costs while maintaining performance
            </div>
        
    </div>
</body>
</html>