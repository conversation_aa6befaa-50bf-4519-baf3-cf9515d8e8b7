import { handler } from './index.mjs';

/**
 * Test suite for validating first frame video element visibility
 * This test specifically addresses the issue where video elements 
 * don't appear in the first few seconds of generated videos
 */

// Test payload with multiple video elements to stress test the system
const testPayload = {
    "elements": [
        {
            "url": "https://a-cdn.thecircleapp.in/production/user-protocol-photos/tysrcp.png",
            "width": 360,
            "height": 358,
            "x": 0,
            "y": 0,
            "type": "photo"
        },
        {
            "url": "https://ruv-cdn.thecircleapp.in/raw/VID20250605114738.mp4",
            "width": 330,
            "height": 188,
            "x": 15,
            "y": 111,
            "type": "video",
            "border": {
                "radius": 20,
                "width": 5,
                "color": "#ff0000"
            }
        },
        {
            "url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
            "width": 200,
            "height": 150,
            "x": 50,
            "y": 50,
            "type": "video",
            "border": {
                "radius": 15,
                "width": 3,
                "color": "#00ff00"
            }
        },
        {
            "url": "https://a-cdn.thecircleapp.in/production/user-protocol-photos/identity.png",
            "width": 360,
            "height": 50,
            "x": 0,
            "y": 308,
            "type": "photo"
        }
    ],
    "frame_height": 358,
    "frame_width": 360,
    "callback_url": "https://httpbin.org/put",
    "scaling_factor": 2.0,
    "job_id": "first-frame-test-" + Date.now()
};

// Test scenarios that commonly cause first frame issues
const testScenarios = [
    {
        name: "Multiple Video Elements",
        description: "Test with multiple video elements to ensure all appear from frame 0",
        payload: testPayload
    },
    {
        name: "Large Video File",
        description: "Test with a larger video file that might take time to load",
        payload: {
            ...testPayload,
            elements: [
                ...testPayload.elements,
                {
                    "url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4",
                    "width": 150,
                    "height": 100,
                    "x": 200,
                    "y": 200,
                    "type": "video",
                    "border": {
                        "radius": 10,
                        "width": 2,
                        "color": "#0000ff"
                    }
                }
            ],
            job_id: "large-video-test-" + Date.now()
        }
    },
    {
        name: "High Scaling Factor",
        description: "Test with high scaling factor to ensure border rendering works correctly",
        payload: {
            ...testPayload,
            scaling_factor: 4.0,
            job_id: "high-scaling-test-" + Date.now()
        }
    }
];

async function runFirstFrameValidationTests() {
    console.log('🎬 Starting First Frame Validation Tests');
    console.log('=' .repeat(60));
    
    for (const scenario of testScenarios) {
        console.log(`\n📋 Running Test: ${scenario.name}`);
        console.log(`📝 Description: ${scenario.description}`);
        console.log(`🆔 Job ID: ${scenario.payload.job_id}`);
        
        try {
            const startTime = Date.now();
            
            // Run the video generation
            const result = await handler(scenario.payload);
            
            const duration = Date.now() - startTime;
            console.log(`⏱️  Total processing time: ${duration}ms`);
            
            if (result.statusCode === 200) {
                const responseBody = JSON.parse(result.body);
                
                console.log('✅ Test PASSED');
                console.log(`📹 Video URL: ${responseBody.video_url}`);
                console.log(`🖼️  Thumbnail URL: ${responseBody.thumbnail_url}`);
                console.log(`📐 Dimensions: ${responseBody.width}x${responseBody.height}`);
                console.log(`⏰ Duration: ${responseBody.duration}s`);
                console.log(`📊 Bitrate: ${responseBody.bitrate} bps`);
                
                // Validate that all expected elements should be visible from frame 0
                const videoElements = scenario.payload.elements.filter(e => e.type === 'video');
                console.log(`🎯 Video elements processed: ${videoElements.length}`);
                
                if (responseBody.duration > 0) {
                    console.log('✅ Video has valid duration - first frame should contain all elements');
                } else {
                    console.log('⚠️  Warning: Video duration is 0 - may indicate processing issues');
                }
                
            } else {
                console.log('❌ Test FAILED');
                console.log(`Status: ${result.statusCode}`);
                console.log(`Error: ${result.body}`);
            }
            
        } catch (error) {
            console.log('❌ Test FAILED with exception');
            console.error(`Error: ${error.message}`);
        }
        
        console.log('-'.repeat(60));
    }
    
    console.log('\n🏁 First Frame Validation Tests Complete');
    
    // Summary of improvements
    console.log('\n📈 Improvements Implemented:');
    console.log('✅ Parallel media downloading for faster processing');
    console.log('✅ Video preloading and readiness validation');
    console.log('✅ First frame content validation');
    console.log('✅ Enhanced FFmpeg synchronization options');
    console.log('✅ Improved error handling for video loading failures');
    console.log('✅ Timeout mechanisms for slow-loading videos');
    console.log('✅ Optimized scaling and border rendering pipeline');
}

// Run the tests
runFirstFrameValidationTests().catch(console.error);
