#!/usr/bin/env node

import { downloadMedia, getCacheStats, clearMediaCache, cleanupExpiredCache } from './general-video-processing-utilities.mjs';
import { ulid } from 'ulid';

/**
 * Test script to demonstrate media caching functionality
 */

async function testCaching() {
    console.log('🗄️  Media Caching Test');
    console.log('=====================');

    const testUrls = [
        'https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png',
        'https://ruv-cdn.thecircleapp.in/assets01/5ff3d6d048f12557bdb559a20f32dc8e.mp4',
        'https://a-cdn.thecircleapp.in/production/photos/133280/b2e78dee-4b08-4448-9909-e3eb8a7df991.jpg'
    ];

    const jobId = ulid();
    const callbackUrl = 'https://example.com/callback';

    try {
        // Clear cache to start fresh
        console.log('\n1. Clearing cache...');
        await clearMediaCache();
        
        let stats = getCacheStats();
        console.log('Initial cache stats:', stats);

        // First download - should be cache miss
        console.log('\n2. First download (cache miss expected)...');
        const startTime1 = Date.now();
        
        const downloadPromises1 = testUrls.map(url => 
            downloadMedia(url, jobId, callbackUrl)
        );
        
        const results1 = await Promise.all(downloadPromises1);
        const duration1 = Date.now() - startTime1;
        
        console.log(`✅ First download completed in ${duration1}ms`);
        console.log('Downloaded files:', results1.map(path => path.split('/').pop()));
        
        stats = getCacheStats();
        console.log('Cache stats after first download:', stats);

        // Second download - should be cache hit
        console.log('\n3. Second download (cache hit expected)...');
        const startTime2 = Date.now();
        
        const downloadPromises2 = testUrls.map(url => 
            downloadMedia(url, jobId + '-2', callbackUrl)
        );
        
        const results2 = await Promise.all(downloadPromises2);
        const duration2 = Date.now() - startTime2;
        
        console.log(`✅ Second download completed in ${duration2}ms`);
        console.log('Downloaded files:', results2.map(path => path.split('/').pop()));
        
        stats = getCacheStats();
        console.log('Cache stats after second download:', stats);

        // Performance comparison
        const speedup = ((duration1 - duration2) / duration1 * 100).toFixed(1);
        console.log(`\n📊 Performance Improvement: ${speedup}% faster (${duration1}ms → ${duration2}ms)`);

        // Test cache cleanup
        console.log('\n4. Testing cache cleanup...');
        await cleanupExpiredCache();
        
        stats = getCacheStats();
        console.log('Cache stats after cleanup:', stats);

        // Test with different job IDs to ensure isolation
        console.log('\n5. Testing job isolation...');
        const jobId3 = ulid();
        const result3 = await downloadMedia(testUrls[0], jobId3, callbackUrl);
        console.log(`✅ Job isolation test completed: ${result3.split('/').pop()}`);

        console.log('\n🎉 All cache tests completed successfully!');
        
        // Final stats
        stats = getCacheStats();
        console.log('\nFinal cache statistics:');
        console.log(`- Total entries: ${stats.totalEntries}`);
        console.log(`- Valid entries: ${stats.validEntries}`);
        console.log(`- Cache size: ${stats.totalSizeMB} MB`);
        console.log(`- Cache TTL: ${stats.ttlMinutes} minutes`);
        console.log(`- Cache directory: ${stats.cacheDir}`);

    } catch (error) {
        console.error('❌ Cache test failed:', error.message);
        console.error(error.stack);
    }
}

async function benchmarkCaching() {
    console.log('\n🏃‍♂️ Cache Performance Benchmark');
    console.log('================================');

    const testUrl = 'https://ruv-cdn.thecircleapp.in/assets01/5ff3d6d048f12557bdb559a20f32dc8e.mp4';
    const iterations = 5;
    const callbackUrl = 'https://example.com/callback';

    // Clear cache
    await clearMediaCache();

    // Benchmark without cache (first download)
    console.log('\nBenchmarking without cache...');
    const noCacheTimes = [];
    
    for (let i = 0; i < iterations; i++) {
        await clearMediaCache(); // Clear cache for each iteration
        const jobId = ulid();
        const startTime = Date.now();
        await downloadMedia(testUrl, jobId, callbackUrl);
        const duration = Date.now() - startTime;
        noCacheTimes.push(duration);
        console.log(`  Iteration ${i + 1}: ${duration}ms`);
    }

    // Benchmark with cache (subsequent downloads)
    console.log('\nBenchmarking with cache...');
    const cacheTimes = [];
    
    // First download to populate cache
    await downloadMedia(testUrl, ulid(), callbackUrl);
    
    for (let i = 0; i < iterations; i++) {
        const jobId = ulid();
        const startTime = Date.now();
        await downloadMedia(testUrl, jobId, callbackUrl);
        const duration = Date.now() - startTime;
        cacheTimes.push(duration);
        console.log(`  Iteration ${i + 1}: ${duration}ms`);
    }

    // Calculate averages
    const avgNoCache = noCacheTimes.reduce((a, b) => a + b, 0) / noCacheTimes.length;
    const avgWithCache = cacheTimes.reduce((a, b) => a + b, 0) / cacheTimes.length;
    const improvement = ((avgNoCache - avgWithCache) / avgNoCache * 100).toFixed(1);

    console.log('\n📈 Benchmark Results:');
    console.log(`Average without cache: ${avgNoCache.toFixed(0)}ms`);
    console.log(`Average with cache: ${avgWithCache.toFixed(0)}ms`);
    console.log(`Performance improvement: ${improvement}%`);
    console.log(`Speed multiplier: ${(avgNoCache / avgWithCache).toFixed(1)}x faster`);
}

// Run tests
if (import.meta.url === `file://${process.argv[1]}`) {
    (async () => {
        await testCaching();
        await benchmarkCaching();
    })().catch(console.error);
}
