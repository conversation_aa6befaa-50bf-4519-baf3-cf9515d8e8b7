#!/usr/bin/env node

import { downloadMedia, getCacheStats, clearMediaCache } from './general-video-processing-utilities.mjs';
import { ulid } from 'ulid';

/**
 * Test script to verify media caching functionality
 */

async function testCaching() {
    console.log('🗄️  Media Caching Test');
    console.log('=====================');

    const testUrl = 'https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png';
    const jobId = ulid();
    const callbackUrl = 'https://example.com/callback';

    try {
        // Clear cache to start fresh
        console.log('\n1. Clearing cache...');
        await clearMediaCache();

        let stats = getCacheStats();
        console.log('Initial cache stats:', {
            totalEntries: stats.totalEntries,
            validEntries: stats.validEntries,
            totalSizeMB: stats.totalSizeMB
        });

        // First download - should be cache miss
        console.log('\n2. First download (cache miss expected)...');
        const startTime1 = Date.now();
        const result1 = await downloadMedia(testUrl, jobId, callbackUrl);
        const duration1 = Date.now() - startTime1;

        console.log(`✅ First download completed in ${duration1}ms`);
        console.log('Downloaded file:', result1.split('/').pop());

        stats = getCacheStats();
        console.log('Cache stats after first download:', {
            totalEntries: stats.totalEntries,
            validEntries: stats.validEntries,
            totalSizeMB: stats.totalSizeMB
        });

        // Second download - should be cache hit
        console.log('\n3. Second download (cache hit expected)...');
        const startTime2 = Date.now();
        const result2 = await downloadMedia(testUrl, jobId + '-2', callbackUrl);
        const duration2 = Date.now() - startTime2;

        console.log(`✅ Second download completed in ${duration2}ms`);
        console.log('Downloaded file:', result2.split('/').pop());

        stats = getCacheStats();
        console.log('Cache stats after second download:', {
            totalEntries: stats.totalEntries,
            validEntries: stats.validEntries,
            totalSizeMB: stats.totalSizeMB
        });

        // Performance comparison
        const speedup = duration1 > duration2 ? ((duration1 - duration2) / duration1 * 100).toFixed(1) : 0;
        console.log(`\n📊 Performance: First=${duration1}ms, Second=${duration2}ms`);
        if (speedup > 0) {
            console.log(`🚀 Cache speedup: ${speedup}% faster`);
        }

        console.log('\n🎉 Cache test completed!');

        return { duration1, duration2, speedup };

    } catch (error) {
        console.error('❌ Cache test failed:', error.message);
        throw error;
    }
}

// Run test
if (import.meta.url === `file://${process.argv[1]}`) {
    testCaching().catch(console.error);
}

export { testCaching };
