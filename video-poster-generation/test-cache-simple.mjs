#!/usr/bin/env node

import { downloadMedia, getCacheStats, clearMediaCache } from './general-video-processing-utilities.mjs';
import { ulid } from 'ulid';

/**
 * Simple cache test with multiple downloads
 */

async function testMultipleDownloads() {
    console.log('🗄️  Multiple Downloads Cache Test');
    console.log('=================================');

    const testUrls = [
        'https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png',
        'https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png', // Same URL again
        'https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png'  // Same URL third time
    ];
    
    const callbackUrl = 'https://example.com/callback';

    try {
        // Clear cache
        console.log('\n1. Clearing cache...');
        await clearMediaCache();
        
        console.log('Initial cache stats:', getCacheStats());

        // Download same URL multiple times
        console.log('\n2. Downloading same URL 3 times...');
        
        for (let i = 0; i < testUrls.length; i++) {
            const jobId = ulid();
            const startTime = Date.now();
            
            const result = await downloadMedia(testUrls[i], jobId, callbackUrl);
            const duration = Date.now() - startTime;
            
            console.log(`Download ${i + 1}: ${duration}ms - ${result.split('/').pop()}`);
            
            const stats = getCacheStats();
            console.log(`  Cache: ${stats.totalEntries} entries, ${stats.validEntries} valid`);
        }

        console.log('\n3. Final cache statistics:');
        const finalStats = getCacheStats();
        console.log(finalStats);

        console.log('\n🎉 Multiple downloads test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        throw error;
    }
}

// Run test
if (import.meta.url === `file://${process.argv[1]}`) {
    testMultipleDownloads().catch(console.error);
}

export { testMultipleDownloads };
