import { tmpdir } from 'node:os';
import { join } from 'node:path';
import { createHash } from 'node:crypto';
import fs from 'node:fs/promises';
import { existsSync } from 'node:fs';

/**
 * Media Cache Manager
 * Caches downloaded media files for 1 hour to improve performance
 */

class MediaCache {
    constructor(cacheDir = join(tmpdir(), 'media-cache'), ttlMinutes = 60) {
        this.cacheDir = cacheDir;
        this.ttlMs = ttlMinutes * 60 * 1000; // Convert to milliseconds
        this.metadata = new Map(); // In-memory metadata cache
        this.initPromise = this.init();
    }

    async init() {
        try {
            await fs.mkdir(this.cacheDir, { recursive: true });
            await this.loadMetadata();
            await this.cleanupExpired();
        } catch (error) {
            console.warn('Cache initialization failed:', error.message);
        }
    }

    /**
     * Generate cache key from URL
     */
    getCacheKey(url) {
        return createHash('sha256').update(url).digest('hex');
    }

    /**
     * Get cache file path
     */
    getCacheFilePath(cacheKey, extension) {
        return join(this.cacheDir, `${cacheKey}${extension}`);
    }

    /**
     * Get metadata file path
     */
    getMetadataFilePath() {
        return join(this.cacheDir, 'cache-metadata.json');
    }

    /**
     * Load metadata from disk
     */
    async loadMetadata() {
        try {
            const metadataPath = this.getMetadataFilePath();
            if (existsSync(metadataPath)) {
                const data = await fs.readFile(metadataPath, 'utf8');
                const metadata = JSON.parse(data);
                this.metadata = new Map(Object.entries(metadata));
            }
        } catch (error) {
            console.warn('Failed to load cache metadata:', error.message);
            this.metadata = new Map();
        }
    }

    /**
     * Save metadata to disk
     */
    async saveMetadata() {
        try {
            const metadataPath = this.getMetadataFilePath();
            const data = JSON.stringify(Object.fromEntries(this.metadata));
            await fs.writeFile(metadataPath, data, 'utf8');
        } catch (error) {
            console.warn('Failed to save cache metadata:', error.message);
        }
    }

    /**
     * Check if cache entry is valid (not expired)
     */
    isValid(cacheKey) {
        const entry = this.metadata.get(cacheKey);
        if (!entry) return false;
        
        const now = Date.now();
        const isExpired = (now - entry.timestamp) > this.ttlMs;
        
        if (isExpired) {
            this.metadata.delete(cacheKey);
            return false;
        }
        
        // Check if file still exists
        const filePath = this.getCacheFilePath(cacheKey, entry.extension);
        return existsSync(filePath);
    }

    /**
     * Get cached file path if valid
     */
    async get(url) {
        await this.initPromise;
        
        const cacheKey = this.getCacheKey(url);
        
        if (!this.isValid(cacheKey)) {
            return null;
        }
        
        const entry = this.metadata.get(cacheKey);
        const filePath = this.getCacheFilePath(cacheKey, entry.extension);
        
        console.log(`Cache HIT for ${url.substring(0, 50)}...`);
        return filePath;
    }

    /**
     * Store file in cache
     */
    async set(url, filePath, extension) {
        await this.initPromise;
        
        try {
            const cacheKey = this.getCacheKey(url);
            const cacheFilePath = this.getCacheFilePath(cacheKey, extension);
            
            // Copy file to cache directory
            await fs.copyFile(filePath, cacheFilePath);
            
            // Update metadata
            this.metadata.set(cacheKey, {
                url,
                extension,
                timestamp: Date.now(),
                originalPath: filePath
            });
            
            // Save metadata to disk
            await this.saveMetadata();
            
            console.log(`Cache STORE for ${url.substring(0, 50)}...`);
            return cacheFilePath;
        } catch (error) {
            console.warn('Failed to cache file:', error.message);
            return filePath; // Return original path if caching fails
        }
    }

    /**
     * Clean up expired cache entries
     */
    async cleanupExpired() {
        const now = Date.now();
        const expiredKeys = [];
        
        for (const [cacheKey, entry] of this.metadata.entries()) {
            const isExpired = (now - entry.timestamp) > this.ttlMs;
            if (isExpired) {
                expiredKeys.push(cacheKey);
            }
        }
        
        for (const cacheKey of expiredKeys) {
            await this.remove(cacheKey);
        }
        
        if (expiredKeys.length > 0) {
            console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
            await this.saveMetadata();
        }
    }

    /**
     * Remove specific cache entry
     */
    async remove(cacheKey) {
        const entry = this.metadata.get(cacheKey);
        if (entry) {
            try {
                const filePath = this.getCacheFilePath(cacheKey, entry.extension);
                await fs.unlink(filePath);
            } catch (error) {
                // File might already be deleted, ignore error
            }
            this.metadata.delete(cacheKey);
        }
    }

    /**
     * Clear entire cache
     */
    async clear() {
        await this.initPromise;
        
        try {
            await fs.rm(this.cacheDir, { recursive: true, force: true });
            await fs.mkdir(this.cacheDir, { recursive: true });
            this.metadata.clear();
            console.log('Cache cleared successfully');
        } catch (error) {
            console.warn('Failed to clear cache:', error.message);
        }
    }

    /**
     * Get cache statistics
     */
    getStats() {
        const now = Date.now();
        let totalSize = 0;
        let validEntries = 0;
        let expiredEntries = 0;
        
        for (const [cacheKey, entry] of this.metadata.entries()) {
            const isExpired = (now - entry.timestamp) > this.ttlMs;
            if (isExpired) {
                expiredEntries++;
            } else {
                validEntries++;
                try {
                    const filePath = this.getCacheFilePath(cacheKey, entry.extension);
                    if (existsSync(filePath)) {
                        const stats = require('fs').statSync(filePath);
                        totalSize += stats.size;
                    }
                } catch (error) {
                    // Ignore errors for stats
                }
            }
        }
        
        return {
            totalEntries: this.metadata.size,
            validEntries,
            expiredEntries,
            totalSizeBytes: totalSize,
            totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
            cacheDir: this.cacheDir,
            ttlMinutes: this.ttlMs / (60 * 1000)
        };
    }
}

// Create singleton instance
const mediaCache = new MediaCache();

export { mediaCache };
export default MediaCache;
