#!/usr/bin/env node

import { downloadMedia, getCacheStats, clearMediaCache } from './general-video-processing-utilities.mjs';
import { ulid } from 'ulid';

/**
 * Test script to verify video caching performance
 */

async function testVideoCaching() {
    console.log('🎬 Video Caching Performance Test');
    console.log('=================================');

    const videoUrl = 'https://ruv-cdn.thecircleapp.in/assets01/5ff3d6d048f12557bdb559a20f32dc8e.mp4';
    const imageUrl = 'https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png';
    const jobId = ulid();
    const callbackUrl = 'https://example.com/callback';

    try {
        // Clear cache to start fresh
        console.log('\n1. Clearing cache...');
        await clearMediaCache();
        
        let stats = getCacheStats();
        console.log('Initial cache stats:', {
            totalEntries: stats.totalEntries,
            validEntries: stats.validEntries,
            totalSizeMB: stats.totalSizeMB
        });

        // Test video download performance
        console.log('\n2. First video download (cache miss)...');
        const startTime1 = Date.now();
        const result1 = await downloadMedia(videoUrl, jobId, callbackUrl);
        const duration1 = Date.now() - startTime1;
        
        console.log(`✅ First video download: ${duration1}ms`);
        console.log('Downloaded file:', result1.split('/').pop());
        
        stats = getCacheStats();
        console.log('Cache stats after video download:', {
            totalEntries: stats.totalEntries,
            validEntries: stats.validEntries,
            totalSizeMB: stats.totalSizeMB
        });

        // Second video download - should be cached
        console.log('\n3. Second video download (cache hit)...');
        const startTime2 = Date.now();
        const result2 = await downloadMedia(videoUrl, jobId + '-2', callbackUrl);
        const duration2 = Date.now() - startTime2;
        
        console.log(`✅ Second video download: ${duration2}ms`);
        console.log('Downloaded file:', result2.split('/').pop());

        // Test image download
        console.log('\n4. Image download (cache miss)...');
        const startTime3 = Date.now();
        const result3 = await downloadMedia(imageUrl, jobId + '-3', callbackUrl);
        const duration3 = Date.now() - startTime3;
        
        console.log(`✅ Image download: ${duration3}ms`);
        console.log('Downloaded file:', result3.split('/').pop());

        // Second image download - should be cached
        console.log('\n5. Second image download (cache hit)...');
        const startTime4 = Date.now();
        const result4 = await downloadMedia(imageUrl, jobId + '-4', callbackUrl);
        const duration4 = Date.now() - startTime4;
        
        console.log(`✅ Second image download: ${duration4}ms`);
        console.log('Downloaded file:', result4.split('/').pop());

        // Final stats
        stats = getCacheStats();
        console.log('\nFinal cache stats:', {
            totalEntries: stats.totalEntries,
            validEntries: stats.validEntries,
            totalSizeMB: stats.totalSizeMB,
            cacheDir: stats.cacheDir
        });

        // Performance summary
        const videoSpeedup = duration1 > duration2 ? ((duration1 - duration2) / duration1 * 100).toFixed(1) : 0;
        const imageSpeedup = duration3 > duration4 ? ((duration3 - duration4) / duration3 * 100).toFixed(1) : 0;
        
        console.log('\n📊 Performance Summary:');
        console.log(`Video: ${duration1}ms → ${duration2}ms (${videoSpeedup}% faster)`);
        console.log(`Image: ${duration3}ms → ${duration4}ms (${imageSpeedup}% faster)`);
        
        const totalOriginal = duration1 + duration3;
        const totalCached = duration2 + duration4;
        const overallSpeedup = ((totalOriginal - totalCached) / totalOriginal * 100).toFixed(1);
        
        console.log(`Overall: ${totalOriginal}ms → ${totalCached}ms (${overallSpeedup}% faster)`);

        console.log('\n🎉 Video cache test completed successfully!');
        
        return { 
            videoSpeedup, 
            imageSpeedup, 
            overallSpeedup,
            cacheStats: stats
        };

    } catch (error) {
        console.error('❌ Video cache test failed:', error.message);
        throw error;
    }
}

// Run test
if (import.meta.url === `file://${process.argv[1]}`) {
    testVideoCaching().catch(console.error);
}

export { testVideoCaching };
