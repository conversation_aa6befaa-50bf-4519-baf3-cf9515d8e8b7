import ffmpeg from 'fluent-ffmpeg';
import axios from 'axios';
import { join } from 'node:path';
import { tmpdir } from 'node:os';
import { S3Client } from '@aws-sdk/client-s3';
import {
    downloadMedia,
    uploadToS3,
    generateThumbnail,
    removeFolderRecursively,
    getVideoMetadata
} from './general-video-processing-utilities.mjs';
import { ERROR_CODES, sendErrorCallback, validateIncomingData } from './error-handling-utilities.mjs';

const PROCESS_TIMEOUT_TIME = 300000; // 5 minutes

/**
 * Apply scaling factor to payload elements and frame dimensions
 */
const applyScaling = (payload, frameWidth, frameHeight, scalingFactor) => {
    if (scalingFactor === 1.0) {
        return { scaledPayload: payload, scaledFrameWidth: frameWidth, scaledFrameHeight: frameHeight };
    }

    console.log(`Applying scaling factor: ${scalingFactor}`);

    const scaledPayload = payload.map((element, index) => {
        const scaledElement = {
            ...element,
            width: Math.round(element.width * scalingFactor),
            height: Math.round(element.height * scalingFactor),
            x: Math.round(element.x * scalingFactor),
            y: Math.round(element.y * scalingFactor)
        };

        if (element.type === 'video' && element.border) {
            scaledElement.border = {
                ...element.border,
                radius: Math.round(element.border.radius * scalingFactor),
                width: Math.round(element.border.width * scalingFactor)
            };
        }

        console.log(
            `Element ${index} scaled: ${element.width}x${element.height} -> ${scaledElement.width}x${scaledElement.height}, ` +
            `position: (${element.x}, ${element.y}) -> (${scaledElement.x}, ${scaledElement.y})`
        );

        return scaledElement;
    });

    const scaledFrameWidth = Math.round(frameWidth * scalingFactor);
    const scaledFrameHeight = Math.round(frameHeight * scalingFactor);

    console.log(`Frame dimensions scaled: ${frameWidth}x${frameHeight} -> ${scaledFrameWidth}x${scaledFrameHeight}`);

    return { scaledPayload, scaledFrameWidth, scaledFrameHeight };
};

export const createMediaComposition = async (
    payload,
    frameWidth,
    frameHeight,
    outputPath,
    jobId,
    callbackUrl
) => {
    try {
        // 1) Download all media
        const mediaFiles = [];
        for (let i = 0; i < payload.length; i++) {
            const element = payload[i];
            const filePath = await downloadMedia(element.url, jobId, callbackUrl);
            mediaFiles.push({ ...element, filePath, index: i });
        }

        // 2) Find max duration & first video with audio
        let maxDuration = 5;
        let firstVideoWithAudio = null;
        for (const m of mediaFiles) {
            if (m.type === 'video') {
                const meta = await getVideoMetadata(m.filePath, jobId, callbackUrl);
                m.metadata = meta;
                maxDuration = Math.max(maxDuration, meta.duration);
                if (!firstVideoWithAudio && meta.hasAudio) {
                    firstVideoWithAudio = m;
                }
            }
        }

        // 3) Build ffmpeg command with a constant black canvas input for background
        let ff = ffmpeg()
            .input(`color=black:size=${frameWidth}x${frameHeight}:duration=${maxDuration}`)
            .inputOptions(['-f', 'lavfi'])
            // enable multi-threaded filter processing with optimizations
            .addOption('-filter_threads', '0')
            .addOption('-filter_complex_threads', '0')  // parallel filter processing
            .addOption('-max_muxing_queue_size', '1024') // prevent buffer issues

        // 4) Load payload images/videos (inputs 1…N) with minimal probe time
        mediaFiles.forEach(m => {
            if (m.type === 'video') {
                ff = ff.input(m.filePath)
                       .inputOptions([
                           '-analyzeduration', '0',     // skip analysis for speed
                           '-probesize', '32',          // minimal probe
                           '-fflags', '+fastseek',      // enable fast seeking
                           '-fflags', '+discardcorrupt' // skip corrupted frames
                       ]);
            } else {
                ff = ff
                    .input(m.filePath)
                    .inputOptions([
                        '-loop', '1',
                        '-t', String(maxDuration),
                        '-fflags', '+fastseek'       // fast seeking for images too
                    ]);
            }
        });

        // 5) Load the audio-bearing video so its audio is mapped (if available)
        let audioInputIndex = -1;
        if (firstVideoWithAudio) {
            audioInputIndex = mediaFiles.length + 1;
            ff = ff.input(firstVideoWithAudio.filePath)
                   .inputOptions([
                       '-analyzeduration', '0',     // skip analysis for speed
                       '-probesize', '32',          // minimal probe
                       '-fflags', '+fastseek',      // enable fast seeking
                       '-fflags', '+discardcorrupt' // skip corrupted frames
                   ]);
        }

        // 6) Build the filter_complex string for overlay stacking
        let fc = '';
        let last = '0:v';

        mediaFiles.forEach((m, idx) => {
            const inp = idx + 1;
            const aspectRatioMode = m.aspect_ratio_mode;
            // Use fastest scaling algorithms for maximum speed
            const scalingFlags = 'fast_bilinear';

            if (aspectRatioMode === 'fill') {
                fc += `[${inp}:v]scale=${m.width}:${m.height}:flags=${scalingFlags}[scaled${idx}];`;
            } else if (aspectRatioMode === 'preserve' || aspectRatioMode === 'original') {
                fc +=
                    `[${inp}:v]scale=${m.width}:${m.height}:force_original_aspect_ratio=decrease:flags=${scalingFlags}[tmp${idx}];` +
                    `[tmp${idx}]pad=${m.width}:${m.height}:(ow-iw)/2:(oh-ih)/2:color=black[scaled${idx}];`;
            } else {
                fc +=
                    `[${inp}:v]scale=${m.width}:${m.height}:force_original_aspect_ratio=increase:flags=${scalingFlags}[tmp${idx}];` +
                    `[tmp${idx}]crop=${m.width}:${m.height}[scaled${idx}];`;
            }

            let proc = `scaled${idx}`;

            if (m.type === 'video' && m.border) {
                let { radius = 0, width: bw = 0 } = m.border;
                const maxR = Math.floor(Math.min(m.width, m.height) / 2);
                if (radius > maxR) radius = maxR;
                const padColor = '0x00000000';
                const TW = m.width + bw * 2;
                const TH = m.height + bw * 2;

                fc += `[scaled${idx}]pad=${TW}:${TH}:${bw}:${bw}:color=${padColor}[padded${idx}];`;
                proc = `padded${idx}`;

                if (radius > 5) {
                    const geq = radius <= 15
                        ? `geq=lum='p(X,Y)':a='if(gt(abs(W/2-X),W/2-${radius})*gt(abs(H/2-Y),H/2-${radius}),if(lte(sqrt(pow(${radius}-(W/2-abs(W/2-X)),2)+pow(${radius}-(H/2-abs(H/2-Y)),2)),${radius}),255,0),255)'`
                        : `geq=lum='p(X,Y)':a='if(gt(abs(W/2-X),W/2-${radius})*gt(abs(H/2-Y),H/2-${radius}),if(lte(hypot(${radius}-(W/2-abs(W/2-X)),${radius}-(H/2-abs(H/2-Y))),${radius}),255,0),255)'`;
                    fc += `[padded${idx}]format=yuva420p,${geq}[processed${idx}];`;
                    proc = `processed${idx}`;
                }
            }

            fc += `[${last}][${proc}]overlay=${m.x}:${m.y}:shortest=1[out${idx}];`;
            last = `out${idx}`;
        });

        if (fc.endsWith(';')) fc = fc.slice(0, -1);
        console.log('Filter complex →', fc);

        const ffmpegStartTime = Date.now();

        return new Promise((resolve, reject) => {
            const opts = [
                '-c:v', 'libx264',
                '-preset', 'ultrafast',       // fastest encoding
                '-crf', '28',                 // lower quality for speed
                '-pix_fmt', 'yuv420p',
                '-profile:v', 'baseline',     // simpler profile
                '-level', '3.0',              // compatibility level
                '-threads', '0',              // utilize all cores
                '-movflags', '+faststart',    // enable progressive streaming
                // Additional speed optimizations
                '-tune', 'fastdecode',        // optimize for fast decoding
                '-x264-params', 'nal-hrd=none:filler=0:ref=1:bframes=0:subme=0:me=dia:no-chroma-me:8x8dct=0:trellis=0:fast-pskip=1:mixed-refs=0:weightb=0', // aggressive speed settings
                '-g', '30',                   // smaller GOP size for faster seeking
                '-bf', '0',                   // no B-frames for speed
                '-refs', '1',                 // single reference frame
                '-me_method', 'dia',          // fastest motion estimation
                '-subq', '0',                 // fastest subpixel motion estimation
                '-trellis', '0',              // disable trellis quantization
                '-aq-mode', '0',              // disable adaptive quantization
                '-fast-pskip', '1',           // enable fast P-skip detection
                '-no-mbtree', '1'             // disable macroblock tree ratecontrol
            ];
            if (firstVideoWithAudio) {
                opts.push('-c:a', 'aac', '-b:a', '96k', '-ar', '44100', '-ac', '2', '-map', `${audioInputIndex}:a:0?`);
            } else {
                opts.push('-an');
            }

            ff
                .complexFilter(fc, last)
                .outputOptions(opts)
                .on('error', err => reject(err))
                .on('end', async () => {
                    console.log(`FFmpeg done in ${Date.now() - ffmpegStartTime}ms`);
                    try {
                        const outMeta = await getVideoMetadata(outputPath, jobId, callbackUrl);
                        resolve(outMeta);
                    } catch {
                        resolve({ width: frameWidth, height: frameHeight, duration: maxDuration, bitrate: 0, hasAudio: !!firstVideoWithAudio });
                    }
                })
                .save(outputPath);
        });

    } catch (err) {
        await sendErrorCallback(
            jobId,
            ERROR_CODES.VIDEO_GENERATION_FAILED,
            err.message,
            callbackUrl
        );
        throw new Error(ERROR_CODES.VIDEO_GENERATION_FAILED);
    }
};

export const handler = async event => {
    const { elements, frame_width, frame_height, scaling_factor = 2.0, job_id, callback_url } = event;
    const timeout = new Promise((_, rej) => {
        setTimeout(async () => {
            await sendErrorCallback(job_id, ERROR_CODES.PROCESS_TIMEOUT, 'Timeout', callback_url);
            rej(new Error(ERROR_CODES.PROCESS_TIMEOUT));
        }, PROCESS_TIMEOUT_TIME);
    });

    const work = (async () => {
        try {
            validateIncomingData(event);

            const { scaledPayload, scaledFrameWidth, scaledFrameHeight } = applyScaling(
                elements, frame_width, frame_height, scaling_factor
            );

            const outName = `${job_id}-video.mp4`;
            const outPath = join(tmpdir(), outName);

            const meta = await createMediaComposition(
                scaledPayload,
                scaledFrameWidth,
                scaledFrameHeight,
                outPath,
                job_id,
                callback_url
            );

            await uploadToS3(outPath, 'praja-raw-user-videos', outName, job_id, callback_url);
            const thumbName = await generateThumbnail(
                job_id,
                outPath,
                'circle-app-photos',
                new S3Client({ region: 'ap-south-1' }),
                callback_url
            );
            await removeFolderRecursively(job_id);

            const responsePayload = {
                video_url: `https://ruv-cdn.thecircleapp.in/video-posters/${outName}`,
                thumbnail_url: `https://circle-app-photos.s3.ap-south-1.amazonaws.com/${thumbName}`,
                job_id,
                width: meta.width,
                height: meta.height,
                duration: meta.duration,
                bitrate: meta.bitrate
            };

            if (!/example\.com|httpbin\.org/.test(callback_url)) {
                await axios.put(`${callback_url}/generation-completed`, responsePayload);
            } else {
                console.log('Test callback, payload:', responsePayload);
            }

            return { statusCode: 200, body: JSON.stringify(responsePayload) };
        } catch (err) {
            console.error('Handler error:', err);
            await sendErrorCallback(job_id, err.message, err.message, callback_url);
            return { statusCode: 500, body: JSON.stringify({ error: err.message }) };
        }
    })();

    return Promise.race([timeout, work]);
};
