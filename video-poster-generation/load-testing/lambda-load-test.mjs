#!/usr/bin/env node

import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';
import { CloudWatchClient, GetMetricStatisticsCommand } from '@aws-sdk/client-cloudwatch';
import fs from 'fs/promises';

/**
 * AWS Lambda Load Testing Suite
 * Tests deployed Lambda function with real AWS metrics
 */

class LambdaLoadTester {
    constructor(functionName, region = 'ap-south-1') {
        this.functionName = functionName;
        this.region = region;
        this.lambdaClient = new LambdaClient({ region });
        this.cloudWatchClient = new CloudWatchClient({ region });
        this.results = [];
        this.errors = [];
    }

    async invokeLambda(payload, testId) {
        const startTime = Date.now();
        
        try {
            const command = new InvokeCommand({
                FunctionName: this.functionName,
                Payload: JSON.stringify(payload),
                InvocationType: 'RequestResponse'
            });
            
            console.log(`🚀 Invoking Lambda test ${testId}...`);
            const response = await this.lambdaClient.send(command);
            const endTime = Date.now();
            
            const duration = endTime - startTime;
            const responsePayload = JSON.parse(new TextDecoder().decode(response.Payload));
            
            if (response.StatusCode === 200 && !responsePayload.errorMessage) {
                console.log(`✅ Test ${testId} completed in ${(duration/1000).toFixed(2)}s`);
                
                return {
                    testId,
                    duration: duration / 1000,
                    success: true,
                    statusCode: response.StatusCode,
                    billedDuration: response.LogResult ? this.extractBilledDuration(response.LogResult) : null,
                    memoryUsed: response.LogResult ? this.extractMemoryUsed(response.LogResult) : null,
                    response: responsePayload,
                    timestamp: new Date().toISOString()
                };
            } else {
                throw new Error(responsePayload.errorMessage || 'Lambda execution failed');
            }
            
        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`❌ Test ${testId} failed after ${(duration/1000).toFixed(2)}s: ${error.message}`);
            
            return {
                testId,
                duration: duration / 1000,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    extractBilledDuration(logResult) {
        try {
            const logs = Buffer.from(logResult, 'base64').toString();
            const match = logs.match(/Billed Duration: (\d+) ms/);
            return match ? parseInt(match[1]) : null;
        } catch {
            return null;
        }
    }

    extractMemoryUsed(logResult) {
        try {
            const logs = Buffer.from(logResult, 'base64').toString();
            const match = logs.match(/Max Memory Used: (\d+) MB/);
            return match ? parseInt(match[1]) : null;
        } catch {
            return null;
        }
    }

    async runConcurrentTests(payload, concurrency, testName) {
        console.log(`\n🔄 Running ${concurrency} concurrent Lambda invocations for ${testName}...`);
        
        const startTime = Date.now();
        const promises = [];
        
        for (let i = 0; i < concurrency; i++) {
            const testPayload = {
                ...payload,
                job_id: `lambda-load-test-${testName}-${i + 1}-${Date.now()}`,
                callback_url: "https://httpbin.org/anything"
            };
            
            promises.push(this.invokeLambda(testPayload, `${testName}-${i + 1}`));
        }
        
        const results = await Promise.allSettled(promises);
        const endTime = Date.now();
        
        const successful = [];
        const failed = [];
        
        results.forEach(result => {
            if (result.status === 'fulfilled') {
                if (result.value.success) {
                    successful.push(result.value);
                } else {
                    failed.push(result.value);
                }
            } else {
                failed.push({
                    error: result.reason.message,
                    timestamp: new Date().toISOString()
                });
            }
        });
        
        const totalDuration = (endTime - startTime) / 1000;
        const durations = successful.map(r => r.duration);
        const billedDurations = successful.map(r => r.billedDuration).filter(d => d !== null);
        const memoryUsages = successful.map(r => r.memoryUsed).filter(m => m !== null);
        
        return {
            testName,
            concurrency,
            totalTests: concurrency,
            successful: successful.length,
            failed: failed.length,
            successRate: (successful.length / concurrency * 100).toFixed(2),
            
            // Timing metrics
            totalWallClockTime: totalDuration,
            avgProcessingTime: durations.length > 0 ? (durations.reduce((a, b) => a + b, 0) / durations.length).toFixed(2) : 0,
            minProcessingTime: durations.length > 0 ? Math.min(...durations).toFixed(2) : 0,
            maxProcessingTime: durations.length > 0 ? Math.max(...durations).toFixed(2) : 0,
            
            // AWS-specific metrics
            avgBilledDuration: billedDurations.length > 0 ? (billedDurations.reduce((a, b) => a + b, 0) / billedDurations.length).toFixed(2) : 'N/A',
            maxBilledDuration: billedDurations.length > 0 ? Math.max(...billedDurations) : 'N/A',
            avgMemoryUsed: memoryUsages.length > 0 ? (memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length).toFixed(2) : 'N/A',
            maxMemoryUsed: memoryUsages.length > 0 ? Math.max(...memoryUsages) : 'N/A',
            
            // Throughput
            invocationsPerSecond: (successful.length / totalDuration).toFixed(2),
            
            results: successful,
            errors: failed
        };
    }

    async getCloudWatchMetrics(startTime, endTime) {
        try {
            const metrics = {};
            
            // Get various CloudWatch metrics
            const metricQueries = [
                { MetricName: 'Duration', Statistic: 'Average' },
                { MetricName: 'Duration', Statistic: 'Maximum' },
                { MetricName: 'Errors', Statistic: 'Sum' },
                { MetricName: 'Invocations', Statistic: 'Sum' },
                { MetricName: 'Throttles', Statistic: 'Sum' },
                { MetricName: 'ConcurrentExecutions', Statistic: 'Maximum' }
            ];
            
            for (const query of metricQueries) {
                const command = new GetMetricStatisticsCommand({
                    Namespace: 'AWS/Lambda',
                    MetricName: query.MetricName,
                    Dimensions: [
                        {
                            Name: 'FunctionName',
                            Value: this.functionName
                        }
                    ],
                    StartTime: startTime,
                    EndTime: endTime,
                    Period: 300, // 5 minutes
                    Statistics: [query.Statistic]
                });
                
                const response = await this.cloudWatchClient.send(command);
                metrics[`${query.MetricName}_${query.Statistic}`] = response.Datapoints;
            }
            
            return metrics;
        } catch (error) {
            console.warn('⚠️  Could not fetch CloudWatch metrics:', error.message);
            return {};
        }
    }
}

// Test payloads for Lambda testing
const LAMBDA_TEST_PAYLOADS = {
    simple: {
        payload: [
            {
                url: "https://httpbin.org/image/jpeg",
                width: 720,
                height: 405,
                x: 0,
                y: 0,
                type: "photo"
            }
        ],
        frame_height: 720,
        frame_width: 720
    },
    
    medium: {
        payload: [
            {
                url: "https://httpbin.org/image/jpeg",
                width: 720,
                height: 200,
                x: 0,
                y: 0,
                type: "photo"
            },
            {
                url: "https://httpbin.org/image/png",
                width: 400,
                height: 300,
                x: 50,
                y: 220,
                type: "photo"
            },
            {
                url: "https://httpbin.org/image/jpeg",
                width: 200,
                height: 150,
                x: 500,
                y: 350,
                type: "photo"
            }
        ],
        frame_height: 720,
        frame_width: 720
    }
};

async function runLambdaLoadTests(functionName) {
    console.log('🎬 AWS Lambda Load Testing Suite');
    console.log('================================');
    console.log(`Testing function: ${functionName}\n`);
    
    const tester = new LambdaLoadTester(functionName);
    const allResults = [];
    const testStartTime = new Date();
    
    try {
        // Test different concurrency levels
        const concurrencyLevels = [1, 3, 5, 8, 10];
        
        for (const concurrency of concurrencyLevels) {
            const result = await tester.runConcurrentTests(
                LAMBDA_TEST_PAYLOADS.medium,
                concurrency,
                `concurrency-${concurrency}`
            );
            allResults.push(result);
            
            console.log(`✅ ${concurrency} concurrent: ${result.avgProcessingTime}s avg, ${result.successRate}% success, ${result.invocationsPerSecond} invocations/sec`);
            
            // Wait between tests to avoid throttling
            await new Promise(resolve => setTimeout(resolve, 10000));
        }
        
        const testEndTime = new Date();
        
        // Get CloudWatch metrics
        console.log('\n📊 Fetching CloudWatch metrics...');
        const cloudWatchMetrics = await tester.getCloudWatchMetrics(testStartTime, testEndTime);
        
        // Generate report
        const report = {
            functionName,
            testSuite: 'AWS Lambda Load Test',
            timestamp: new Date().toISOString(),
            testDuration: (testEndTime - testStartTime) / 1000,
            results: allResults,
            cloudWatchMetrics,
            summary: {
                totalInvocations: allResults.reduce((sum, r) => sum + r.totalTests, 0),
                totalSuccessful: allResults.reduce((sum, r) => sum + r.successful, 0),
                overallSuccessRate: 0
            }
        };
        
        report.summary.overallSuccessRate = (
            (report.summary.totalSuccessful / report.summary.totalInvocations) * 100
        ).toFixed(2);
        
        // Save report
        await fs.writeFile(
            'lambda-load-test-report.json',
            JSON.stringify(report, null, 2)
        );
        
        console.log('\n📊 LAMBDA LOAD TEST SUMMARY');
        console.log('===========================');
        console.log(`Total Invocations: ${report.summary.totalInvocations}`);
        console.log(`Success Rate: ${report.summary.overallSuccessRate}%`);
        console.log(`Report saved to: lambda-load-test-report.json`);
        
        return report;
        
    } catch (error) {
        console.error('❌ Lambda load testing failed:', error);
        throw error;
    }
}

export { LambdaLoadTester, runLambdaLoadTests };

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
    const functionName = process.argv[2];
    if (!functionName) {
        console.error('Usage: node lambda-load-test.mjs <function-name>');
        process.exit(1);
    }
    
    runLambdaLoadTests(functionName)
        .then(() => console.log('🎉 Lambda load testing completed!'))
        .catch(error => {
            console.error('❌ Testing failed:', error);
            process.exit(1);
        });
}
