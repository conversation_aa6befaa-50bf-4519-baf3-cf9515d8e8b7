#!/usr/bin/env node

import { handler } from '../index.mjs';
import fs from 'fs/promises';
import { performance } from 'perf_hooks';
import { cpus } from 'os';

/**
 * Load Testing Suite for Video Poster Generation Lambda
 * Tests concurrent processing of 30-second, ~10MB videos
 */

class LoadTestMetrics {
    constructor() {
        this.results = [];
        this.errors = [];
        this.startTime = null;
        this.endTime = null;
    }

    addResult(result) {
        this.results.push(result);
    }

    addError(error) {
        this.errors.push(error);
    }

    getMetrics() {
        if (this.results.length === 0) {
            return { error: 'No successful results to analyze' };
        }

        const durations = this.results.map(r => r.duration);
        const memoryUsages = this.results.map(r => r.memoryUsed || 0);

        return {
            totalTests: this.results.length + this.errors.length,
            successfulTests: this.results.length,
            failedTests: this.errors.length,
            successRate: (this.results.length / (this.results.length + this.errors.length) * 100).toFixed(2),

            // Timing metrics
            totalDuration: this.endTime - this.startTime,
            avgProcessingTime: (durations.reduce((a, b) => a + b, 0) / durations.length).toFixed(2),
            minProcessingTime: Math.min(...durations).toFixed(2),
            maxProcessingTime: Math.max(...durations).toFixed(2),

            // Memory metrics (if available)
            avgMemoryUsage: memoryUsages.length > 0 ? (memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length).toFixed(2) : 'N/A',
            maxMemoryUsage: memoryUsages.length > 0 ? Math.max(...memoryUsages).toFixed(2) : 'N/A',

            // Throughput
            videosPerSecond: (this.results.length / ((this.endTime - this.startTime) / 1000)).toFixed(2),
            videosPerMinute: (this.results.length / ((this.endTime - this.startTime) / 60000)).toFixed(2),

            // Detailed results
            results: this.results,
            errors: this.errors
        };
    }
}

// Test payloads simulating 30-second videos with different complexities
const TEST_PAYLOADS = {
    simple: {
        payload: [
            {
                url: "https://ruv-cdn.thecircleapp.in/assets01/5ff3d6d048f12557bdb559a20f32dc8e.mp4",
                width: 720,
                height: 405,
                x: 0,
                y: 0,
                type: "video",
                border: {
                    radius: 10,
                    size: 3,
                    color: "#000000"
                }
            }
        ],
        frame_height: 720,
        frame_width: 720,
        complexity: "simple"
    },

    medium: {
        payload: [
            {
                url: "https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png",
                width: 720,
                height: 200,
                x: 0,
                y: 0,
                type: "photo"
            },

            {
                url: "https://ruv-cdn.thecircleapp.in/assets01/5ff3d6d048f12557bdb559a20f32dc8e.mp4",
                width: 400,
                height: 300,
                x: 50,
                y: 220,
                type: "video",
                border: {
                    radius: 20,
                    size: 5,
                    color: "#ff0000"
                },
            },
            {
                url: "https://a-cdn.thecircleapp.in/production/photos/133280/b2e78dee-4b08-4448-9909-e3eb8a7df991.jpg",
                width: 200,
                height: 150,
                x: 500,
                y: 350,
                type: "photo"
            }
        ],
        frame_height: 720,
        frame_width: 720,
        complexity: "medium"
    },

    complex: {
        "payload": [
            {
                "url": "https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png",
                "width": 720,
                "height": 1280,
                "x": 0,
                "y": 0,
                "type": "photo"
            },
            {
                "url": "https://a-cdn.thecircleapp.in/production/user-protocol-photos/protocol-298110-1748337600498.png",
                "width": 720,
                "height": 135,
                "x": 0,
                "y": 0,
                "type": "photo"
            },
            {
                "url": "https://ruv-cdn.thecircleapp.in/assets01/5ff3d6d048f12557bdb559a20f32dc8e.mp4",
                "width": 500,
                "height": 700,
                "x": 200,
                "y": 145,
                "type": "video",
                "border": {
                    "radius": 20,
                    "width": 1,
                    "color": "#ff0000"
                }
            },
            {
                "url": "https://a-cdn.thecircleapp.in/production/photos/133280/b2e78dee-4b08-4448-9909-e3eb8a7df991.jpg",
                "width": 300,
                "height": 300,
                "x": 0,
                "y": 655,
                "type": "photo"
            },
            {
                "url": "https://a-cdn.thecircleapp.in/production/user-protocol-photos/identity.png",
                "width": 720,
                "height": 135,
                "x": 0,
                "y": 1145,
                "type": "photo"
            }
        ],
        "frame_height": 1280,
        "frame_width": 720,
        complexity: "complex"
    }
};

async function runSingleTest(testPayload, testId) {
    const startTime = performance.now();
    const memoryBefore = process.memoryUsage();

    try {
        const event = {
            ...testPayload,
            job_id: `${Date.now()}`,
            callback_url: "https://httpbin.org/anything"
        };

        console.log(`🚀 Starting test ${testId} (${testPayload.complexity})...`);

        const result = await handler(event);
        const endTime = performance.now();
        const memoryAfter = process.memoryUsage();

        const duration = endTime - startTime;
        const memoryUsed = memoryAfter.heapUsed - memoryBefore.heapUsed;

        console.log(`✅ Test ${testId} completed in ${(duration / 1000).toFixed(2)}s`);

        return {
            testId,
            complexity: testPayload.complexity,
            duration: duration / 1000, // Convert to seconds
            memoryUsed: memoryUsed / (1024 * 1024), // Convert to MB
            success: true,
            result: result,
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;

        console.log(`❌ Test ${testId} failed after ${(duration / 1000).toFixed(2)}s: ${error.message}`);

        return {
            testId,
            complexity: testPayload.complexity,
            duration: duration / 1000,
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

async function runConcurrentTests(testPayload, concurrency, testName) {
    console.log(`\n🔄 Running ${concurrency} concurrent ${testName} tests...`);

    const metrics = new LoadTestMetrics();
    metrics.startTime = performance.now();

    const promises = [];
    for (let i = 0; i < concurrency; i++) {
        promises.push(runSingleTest(testPayload, `${testName}-${i + 1}`));
    }

    const results = await Promise.allSettled(promises);
    metrics.endTime = performance.now();

    results.forEach(result => {
        if (result.status === 'fulfilled') {
            if (result.value.success) {
                metrics.addResult(result.value);
            } else {
                metrics.addError(result.value);
            }
        } else {
            metrics.addError({
                error: result.reason.message,
                timestamp: new Date().toISOString()
            });
        }
    });

    return metrics.getMetrics();
}

async function runSequentialTests(testPayload, count, testName) {
    console.log(`\n📊 Running ${count} sequential ${testName} tests...`);

    const metrics = new LoadTestMetrics();
    metrics.startTime = performance.now();

    for (let i = 0; i < count; i++) {
        const result = await runSingleTest(testPayload, `${testName}-seq-${i + 1}`);

        if (result.success) {
            metrics.addResult(result);
        } else {
            metrics.addError(result);
        }
    }

    metrics.endTime = performance.now();
    return metrics.getMetrics();
}

async function generateReport(allResults) {
    const report = {
        testSuite: 'Video Poster Generation Load Test',
        timestamp: new Date().toISOString(),
        systemInfo: {
            cpuCores: cpus().length,
            nodeVersion: process.version,
            platform: process.platform
        },
        results: allResults,
        summary: {
            totalTests: allResults.reduce((sum, r) => sum + r.totalTests, 0),
            totalSuccessful: allResults.reduce((sum, r) => sum + r.successfulTests, 0),
            totalFailed: allResults.reduce((sum, r) => sum + r.failedTests, 0),
            overallSuccessRate: 0
        }
    };

    report.summary.overallSuccessRate = (
        (report.summary.totalSuccessful / report.summary.totalTests) * 100
    ).toFixed(2);

    // Save detailed report
    await fs.writeFile(
        'load-test-report.json',
        JSON.stringify(report, null, 2)
    );

    return report;
}

export {
    LoadTestMetrics,
    TEST_PAYLOADS,
    runSingleTest,
    runConcurrentTests,
    runSequentialTests,
    generateReport
};
