#!/usr/bin/env node

import {
    TEST_PAYLOADS,
    runConcurrentTests,
    runSequentialTests,
    generateReport
} from './load-test-suite.mjs';

/**
 * Main Load Test Runner
 * Executes comprehensive load testing scenarios
 */

async function printHeader() {
    console.log('🎬 Video Poster Generation Lambda Load Testing Suite');
    console.log('==================================================');
    console.log('Testing scenarios for 30-second, ~10MB videos\n');
}

async function runBasicPerformanceTests() {
    console.log('📈 PHASE 1: Basic Performance Testing');
    console.log('=====================================');
    
    const results = [];
    
    // Test each complexity level sequentially
    for (const [complexity, payload] of Object.entries(TEST_PAYLOADS)) {
        console.log(`\nTesting ${complexity} complexity...`);
        const result = await runSequentialTests(payload, 3, `baseline-${complexity}`);
        result.testType = `baseline-${complexity}`;
        results.push(result);
        
        console.log(`✅ ${complexity} baseline: ${result.avgProcessingTime}s avg, ${result.successRate}% success`);
    }
    
    return results;
}

async function runConcurrencyTests() {
    console.log('\n🔄 PHASE 2: Concurrency Testing');
    console.log('===============================');
    
    const results = [];
    const concurrencyLevels = [2, 4, 6, 8, 10];
    
    // Test medium complexity at different concurrency levels
    for (const concurrency of concurrencyLevels) {
        console.log(`\nTesting ${concurrency} concurrent jobs...`);
        const result = await runConcurrentTests(
            TEST_PAYLOADS.medium, 
            concurrency, 
            `concurrent-${concurrency}`
        );
        result.testType = `concurrent-${concurrency}`;
        result.concurrencyLevel = concurrency;
        results.push(result);
        
        console.log(`✅ ${concurrency} concurrent: ${result.avgProcessingTime}s avg, ${result.successRate}% success, ${result.videosPerSecond} videos/sec`);
        
        // Add delay between concurrency tests to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    return results;
}

async function runStressTests() {
    console.log('\n💪 PHASE 3: Stress Testing');
    console.log('==========================');
    
    const results = [];
    
    // High concurrency stress test
    console.log('\nRunning high concurrency stress test (12 concurrent)...');
    const stressResult = await runConcurrentTests(
        TEST_PAYLOADS.complex,
        12,
        'stress-high-concurrency'
    );
    stressResult.testType = 'stress-high-concurrency';
    stressResult.concurrencyLevel = 12;
    results.push(stressResult);
    
    console.log(`✅ Stress test: ${stressResult.avgProcessingTime}s avg, ${stressResult.successRate}% success`);
    
    // Sustained load test
    console.log('\nRunning sustained load test (20 sequential complex videos)...');
    const sustainedResult = await runSequentialTests(
        TEST_PAYLOADS.complex,
        20,
        'sustained-load'
    );
    sustainedResult.testType = 'sustained-load';
    results.push(sustainedResult);
    
    console.log(`✅ Sustained load: ${sustainedResult.avgProcessingTime}s avg, ${sustainedResult.successRate}% success`);
    
    return results;
}

async function runMemoryTests() {
    console.log('\n🧠 PHASE 4: Memory Usage Testing');
    console.log('================================');
    
    const results = [];
    
    // Memory stress with complex compositions
    console.log('\nTesting memory usage with complex compositions...');
    const memoryResult = await runConcurrentTests(
        TEST_PAYLOADS.complex,
        6,
        'memory-stress'
    );
    memoryResult.testType = 'memory-stress';
    results.push(memoryResult);
    
    console.log(`✅ Memory test: ${memoryResult.avgProcessingTime}s avg, ${memoryResult.maxMemoryUsage}MB peak memory`);
    
    return results;
}

function printSummaryReport(allResults) {
    console.log('\n📊 LOAD TEST SUMMARY REPORT');
    console.log('===========================');
    
    const totalTests = allResults.reduce((sum, r) => sum + r.totalTests, 0);
    const totalSuccessful = allResults.reduce((sum, r) => sum + r.successfulTests, 0);
    const overallSuccessRate = ((totalSuccessful / totalTests) * 100).toFixed(2);
    
    console.log(`\n📈 Overall Statistics:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Successful: ${totalSuccessful}`);
    console.log(`   Failed: ${totalTests - totalSuccessful}`);
    console.log(`   Success Rate: ${overallSuccessRate}%`);
    
    console.log(`\n⚡ Performance Insights:`);
    
    // Find best and worst performing tests
    const performanceTests = allResults.filter(r => r.avgProcessingTime);
    if (performanceTests.length > 0) {
        const fastest = performanceTests.reduce((min, r) => 
            parseFloat(r.avgProcessingTime) < parseFloat(min.avgProcessingTime) ? r : min
        );
        const slowest = performanceTests.reduce((max, r) => 
            parseFloat(r.avgProcessingTime) > parseFloat(max.avgProcessingTime) ? r : max
        );
        
        console.log(`   Fastest: ${fastest.testType} (${fastest.avgProcessingTime}s avg)`);
        console.log(`   Slowest: ${slowest.testType} (${slowest.avgProcessingTime}s avg)`);
    }
    
    // Concurrency analysis
    const concurrencyTests = allResults.filter(r => r.concurrencyLevel);
    if (concurrencyTests.length > 0) {
        console.log(`\n🔄 Concurrency Analysis:`);
        concurrencyTests.forEach(test => {
            console.log(`   ${test.concurrencyLevel} concurrent: ${test.videosPerSecond} videos/sec, ${test.successRate}% success`);
        });
        
        // Find optimal concurrency level
        const optimalConcurrency = concurrencyTests
            .filter(t => parseFloat(t.successRate) >= 95)
            .reduce((best, current) => 
                parseFloat(current.videosPerSecond) > parseFloat(best.videosPerSecond) ? current : best
            );
        
        if (optimalConcurrency) {
            console.log(`   🎯 Optimal Concurrency: ${optimalConcurrency.concurrencyLevel} (${optimalConcurrency.videosPerSecond} videos/sec)`);
        }
    }
    
    // Memory analysis
    const memoryTests = allResults.filter(r => r.maxMemoryUsage && r.maxMemoryUsage !== 'N/A');
    if (memoryTests.length > 0) {
        const maxMemory = Math.max(...memoryTests.map(r => parseFloat(r.maxMemoryUsage)));
        const avgMemory = (memoryTests.reduce((sum, r) => sum + parseFloat(r.avgMemoryUsage), 0) / memoryTests.length).toFixed(2);
        
        console.log(`\n🧠 Memory Usage:`);
        console.log(`   Peak Memory: ${maxMemory}MB`);
        console.log(`   Average Memory: ${avgMemory}MB`);
        console.log(`   Memory Efficiency: ${((10240 - maxMemory) / 10240 * 100).toFixed(1)}% headroom remaining`);
    }
    
    console.log(`\n💡 Recommendations:`);
    
    if (overallSuccessRate < 95) {
        console.log(`   ⚠️  Success rate below 95% - consider increasing timeout or memory`);
    }
    
    const avgProcessingTime = performanceTests.length > 0 ? 
        (performanceTests.reduce((sum, r) => sum + parseFloat(r.avgProcessingTime), 0) / performanceTests.length).toFixed(2) : 0;
    
    if (avgProcessingTime > 45) {
        console.log(`   ⚠️  Average processing time > 45s - consider optimizing FFmpeg settings`);
    }
    
    if (avgProcessingTime < 30) {
        console.log(`   ✅ Excellent processing performance for 30s videos`);
    }
    
    console.log(`\n📄 Detailed report saved to: load-test-report.json`);
}

async function main() {
    try {
        await printHeader();
        
        const allResults = [];
        
        // Run all test phases
        const basicResults = await runBasicPerformanceTests();
        allResults.push(...basicResults);
        
        const concurrencyResults = await runConcurrencyTests();
        allResults.push(...concurrencyResults);
        
        const stressResults = await runStressTests();
        allResults.push(...stressResults);
        
        const memoryResults = await runMemoryTests();
        allResults.push(...memoryResults);
        
        // Generate comprehensive report
        const report = await generateReport(allResults);
        
        // Print summary
        printSummaryReport(allResults);
        
        console.log('\n🎉 Load testing completed successfully!');
        
    } catch (error) {
        console.error('❌ Load testing failed:', error);
        process.exit(1);
    }
}

// Run the load tests
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { main };
