# Video Poster Generation Load Testing Suite

Comprehensive load testing suite for analyzing the performance and scalability of the video poster generation Lambda function with 30-second, ~10MB videos.

## 🎯 Testing Objectives

- **Performance Analysis**: Measure processing times for different video complexities
- **Concurrency Testing**: Determine optimal concurrent execution levels
- **Memory Usage**: Monitor memory consumption patterns
- **Scalability Assessment**: Identify performance bottlenecks and limits
- **AWS Metrics**: Collect real Lambda execution metrics

## 📁 Test Suite Components

### Core Testing Files

- **`load-test-suite.mjs`** - Main testing framework with local execution
- **`run-load-tests.mjs`** - Comprehensive test runner for local testing
- **`lambda-load-test.mjs`** - AWS Lambda-specific load testing
- **`metrics-analyzer.mjs`** - Advanced metrics analysis and reporting

### Test Scenarios

1. **Baseline Performance** - Sequential processing of different complexities
2. **Concurrency Testing** - 2, 4, 6, 8, 10 concurrent executions
3. **Stress Testing** - High concurrency (12+) and sustained load
4. **Memory Analysis** - Memory usage patterns under load

## 🚀 Quick Start

### Prerequisites

```bash
# Install dependencies
cd video-poster-generation
npm install

# Ensure AWS credentials are configured (for Lambda testing)
aws configure
```

### Local Load Testing

```bash
# Run comprehensive local load tests
cd load-testing
node run-load-tests.mjs
```

### AWS Lambda Load Testing

```bash
# Test deployed Lambda function
node lambda-load-test.mjs your-lambda-function-name

# Example:
node lambda-load-test.mjs video-poster-generation-VideoPosterGeneration-ABC123
```

### Analyze Results

```bash
# Analyze test results and generate detailed reports
node metrics-analyzer.mjs load-test-report.json
```

## 📊 Expected Metrics for 30s/10MB Videos

### Performance Benchmarks

| Metric | Expected Range | Optimal Target |
|--------|----------------|----------------|
| Processing Time | 15-45 seconds | < 30 seconds |
| Memory Usage | 500MB-1.5GB | < 1GB |
| Success Rate | > 95% | > 98% |
| Concurrent Jobs | 5-10 | 8 |

### Throughput Estimates

- **Sequential**: 120-240 videos/hour
- **Concurrent (8 jobs)**: 600-1200 videos/hour
- **Peak Throughput**: ~10-15 videos/minute

## 🔧 Test Configuration

### Current Lambda Settings

```yaml
MemorySize: 10240 MB (10GB)
Timeout: 180 seconds (3 minutes)
EphemeralStorage: 10240 MB (10GB)
```

### Test Payloads

#### Simple Complexity
- 1 video element with basic borders
- Expected: 15-25 seconds processing

#### Medium Complexity
- 1 background photo + 1 video + 1 overlay photo
- Expected: 25-35 seconds processing

#### Complex Complexity
- 2 background photos + 2 videos with borders + 1 overlay
- Expected: 35-45 seconds processing

## 📈 Metrics Collection

### Local Testing Metrics

- **Processing Time**: End-to-end execution time
- **Memory Usage**: Peak memory consumption
- **Success/Failure Rates**: Error tracking
- **Throughput**: Videos processed per second/minute

### AWS Lambda Metrics

- **Billed Duration**: AWS Lambda execution time
- **Max Memory Used**: Actual memory consumption
- **CloudWatch Metrics**: Duration, Errors, Throttles, Concurrent Executions
- **Cost Analysis**: Execution cost per video

### Generated Reports

1. **JSON Report** (`load-test-report.json`)
   - Raw test data and metrics
   - Detailed results for each test

2. **Detailed Analysis** (`detailed-metrics-analysis.json`)
   - Performance analysis
   - Recommendations
   - Scalability insights

3. **HTML Report** (`load-test-report.html`)
   - Visual dashboard
   - Charts and graphs
   - Executive summary

## 🎛️ Customizing Tests

### Modify Test Payloads

Edit `TEST_PAYLOADS` in `load-test-suite.mjs`:

```javascript
const TEST_PAYLOADS = {
    custom: {
        payload: [
            {
                url: "your-video-url.mp4",
                width: 720,
                height: 405,
                x: 0,
                y: 0,
                type: "video",
                border: {
                    radius: 20,
                    width: 5,
                    color: "#ff0000"
                }
            }
        ],
        frame_height: 720,
        frame_width: 720,
        complexity: "custom"
    }
};
```

### Adjust Concurrency Levels

Modify `concurrencyLevels` array in test runners:

```javascript
const concurrencyLevels = [2, 4, 6, 8, 10, 12, 15];
```

### Configure Test Duration

Adjust test counts and delays:

```javascript
// Number of sequential tests per complexity
const sequentialTestCount = 5;

// Delay between concurrency tests (ms)
const delayBetweenTests = 10000;
```

## 🔍 Interpreting Results

### Performance Indicators

- **Green Zone**: < 30s processing, > 95% success rate
- **Yellow Zone**: 30-45s processing, 90-95% success rate
- **Red Zone**: > 45s processing, < 90% success rate

### Optimization Recommendations

Based on test results, the analyzer provides:

1. **Memory Optimization**: Adjust Lambda memory allocation
2. **Timeout Adjustments**: Increase/decrease timeout settings
3. **Concurrency Limits**: Set optimal reserved concurrency
4. **FFmpeg Tuning**: Optimize encoding parameters

## 🚨 Troubleshooting

### Common Issues

1. **Timeout Errors**
   - Increase Lambda timeout to 300s
   - Check video complexity and size

2. **Memory Errors**
   - Monitor memory usage patterns
   - Consider increasing memory allocation

3. **Throttling**
   - Reduce concurrency levels
   - Check AWS account limits

4. **Network Issues**
   - Verify media URLs are accessible
   - Check S3 upload permissions

### Debug Mode

Enable verbose logging:

```bash
export DEBUG=1
node run-load-tests.mjs
```

## 📞 Support

For issues or questions about load testing:

1. Check the troubleshooting section
2. Review CloudWatch logs for Lambda executions
3. Analyze the generated reports for insights
4. Consider adjusting test parameters based on results

## 🔄 Continuous Testing

### Automated Testing

Set up regular load testing:

```bash
# Weekly performance check
0 2 * * 1 cd /path/to/load-testing && node run-load-tests.mjs

# Monthly comprehensive analysis
0 3 1 * * cd /path/to/load-testing && node lambda-load-test.mjs function-name
```

### Performance Monitoring

- Monitor CloudWatch metrics regularly
- Set up alarms for performance degradation
- Track cost per video processing
- Review and update test scenarios based on usage patterns
