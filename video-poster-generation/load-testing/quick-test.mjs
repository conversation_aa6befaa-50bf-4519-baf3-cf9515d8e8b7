#!/usr/bin/env node

import { runConcurrentTests, TEST_PAYLOADS } from './load-test-suite.mjs';
import { analyzeReport } from './metrics-analyzer.mjs';
import fs from 'fs/promises';

/**
 * Quick Load Test Runner
 * Runs a focused test to quickly assess Lambda performance for 30s/10MB videos
 */

async function runQuickTest() {
    console.log('🎬 Quick Load Test for Video Poster Generation');
    console.log('==============================================');
    console.log('Testing 30-second, ~10MB video processing performance\n');

    const results = [];
    const startTime = Date.now();

    try {
        // Test 1: Baseline performance (single execution)
        console.log('📊 Test 1: Baseline Performance');
        console.log('-------------------------------');
        const baselineResult = await runConcurrentTests(TEST_PAYLOADS.medium, 1, 'baseline');
        results.push(baselineResult);
        
        console.log(`✅ Baseline: ${baselineResult.avgProcessingTime}s processing time`);
        console.log(`   Success Rate: ${baselineResult.successRate}%`);
        console.log(`   Memory Usage: ${baselineResult.maxMemoryUsage}MB\n`);

        // Test 2: Moderate concurrency (5 concurrent)
        console.log('🔄 Test 2: Moderate Concurrency (5 concurrent)');
        console.log('----------------------------------------------');
        const concurrentResult = await runConcurrentTests(TEST_PAYLOADS.medium, 5, 'concurrent-5');
        results.push(concurrentResult);
        
        console.log(`✅ Concurrent: ${concurrentResult.avgProcessingTime}s avg processing time`);
        console.log(`   Success Rate: ${concurrentResult.successRate}%`);
        console.log(`   Throughput: ${concurrentResult.videosPerSecond} videos/sec`);
        console.log(`   Memory Usage: ${concurrentResult.maxMemoryUsage}MB\n`);

        // Test 3: Complex composition
        console.log('🎯 Test 3: Complex Composition');
        console.log('------------------------------');
        const complexResult = await runConcurrentTests(TEST_PAYLOADS.complex, 3, 'complex');
        results.push(complexResult);
        
        console.log(`✅ Complex: ${complexResult.avgProcessingTime}s processing time`);
        console.log(`   Success Rate: ${complexResult.successRate}%`);
        console.log(`   Memory Usage: ${complexResult.maxMemoryUsage}MB\n`);

        const endTime = Date.now();
        const totalDuration = (endTime - startTime) / 1000;

        // Generate quick report
        const quickReport = {
            testSuite: 'Quick Load Test',
            timestamp: new Date().toISOString(),
            totalDuration,
            results,
            summary: generateQuickSummary(results)
        };

        // Save report
        await fs.writeFile('quick-test-report.json', JSON.stringify(quickReport, null, 2));

        // Print summary
        printQuickSummary(quickReport);

        return quickReport;

    } catch (error) {
        console.error('❌ Quick test failed:', error);
        throw error;
    }
}

function generateQuickSummary(results) {
    const totalTests = results.reduce((sum, r) => sum + r.totalTests, 0);
    const totalSuccessful = results.reduce((sum, r) => sum + r.successful, 0);
    const overallSuccessRate = ((totalSuccessful / totalTests) * 100).toFixed(2);

    const avgProcessingTimes = results
        .map(r => parseFloat(r.avgProcessingTime))
        .filter(t => !isNaN(t));
    
    const avgProcessingTime = avgProcessingTimes.length > 0 ? 
        (avgProcessingTimes.reduce((a, b) => a + b, 0) / avgProcessingTimes.length).toFixed(2) : 'N/A';

    const memoryUsages = results
        .map(r => parseFloat(r.maxMemoryUsage))
        .filter(m => !isNaN(m));
    
    const maxMemoryUsage = memoryUsages.length > 0 ? Math.max(...memoryUsages).toFixed(2) : 'N/A';

    return {
        totalTests,
        totalSuccessful,
        overallSuccessRate,
        avgProcessingTime,
        maxMemoryUsage,
        recommendations: generateQuickRecommendations(results)
    };
}

function generateQuickRecommendations(results) {
    const recommendations = [];
    
    // Check baseline performance
    const baseline = results.find(r => r.testName === 'baseline');
    if (baseline && parseFloat(baseline.avgProcessingTime) > 45) {
        recommendations.push('⚠️  Baseline processing time > 45s - consider optimizing FFmpeg settings or increasing memory');
    }

    // Check success rates
    const lowSuccessRate = results.find(r => parseFloat(r.successRate) < 95);
    if (lowSuccessRate) {
        recommendations.push('⚠️  Success rate below 95% - consider increasing timeout from 180s to 300s');
    }

    // Check concurrency performance
    const concurrent = results.find(r => r.testName === 'concurrent-5');
    if (concurrent && parseFloat(concurrent.successRate) >= 95) {
        recommendations.push('✅ Good concurrency performance - can handle 5+ concurrent videos');
    }

    // Check memory usage
    const maxMemory = Math.max(...results.map(r => parseFloat(r.maxMemoryUsage)).filter(m => !isNaN(m)));
    if (maxMemory > 8000) {
        recommendations.push('⚠️  High memory usage detected - monitor for potential memory issues');
    } else if (maxMemory < 3000) {
        recommendations.push('💡 Low memory usage - consider reducing Lambda memory allocation to optimize costs');
    }

    if (recommendations.length === 0) {
        recommendations.push('✅ All tests passed with good performance metrics');
    }

    return recommendations;
}

function printQuickSummary(report) {
    console.log('📊 QUICK TEST SUMMARY');
    console.log('=====================');
    console.log(`Total Duration: ${report.totalDuration.toFixed(2)}s`);
    console.log(`Total Tests: ${report.summary.totalTests}`);
    console.log(`Success Rate: ${report.summary.overallSuccessRate}%`);
    console.log(`Avg Processing Time: ${report.summary.avgProcessingTime}s`);
    console.log(`Peak Memory Usage: ${report.summary.maxMemoryUsage}MB`);
    
    console.log('\n💡 RECOMMENDATIONS:');
    report.summary.recommendations.forEach(rec => {
        console.log(`   ${rec}`);
    });

    console.log('\n📄 Detailed report saved to: quick-test-report.json');
    
    // Performance assessment
    const avgTime = parseFloat(report.summary.avgProcessingTime);
    const successRate = parseFloat(report.summary.overallSuccessRate);
    
    console.log('\n🎯 PERFORMANCE ASSESSMENT:');
    
    if (successRate >= 95 && avgTime <= 30) {
        console.log('   🟢 EXCELLENT - Ready for production with 30s/10MB videos');
    } else if (successRate >= 90 && avgTime <= 45) {
        console.log('   🟡 GOOD - Acceptable performance, minor optimizations recommended');
    } else {
        console.log('   🔴 NEEDS IMPROVEMENT - Significant optimizations required');
    }

    // Capacity estimate
    const concurrent = report.results.find(r => r.testName === 'concurrent-5');
    if (concurrent && parseFloat(concurrent.successRate) >= 95) {
        const hourlyCapacity = Math.floor(parseFloat(concurrent.videosPerSecond) * 3600);
        console.log(`\n📈 ESTIMATED CAPACITY:`);
        console.log(`   Concurrent Processing: ~${hourlyCapacity} videos/hour`);
        console.log(`   Daily Capacity: ~${Math.floor(hourlyCapacity * 24 / 1000)}K videos/day`);
    }
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
    runQuickTest()
        .then(() => {
            console.log('\n🎉 Quick test completed successfully!');
            console.log('\nNext steps:');
            console.log('1. Review the recommendations above');
            console.log('2. Run full load tests: node run-load-tests.mjs');
            console.log('3. Test deployed Lambda: node lambda-load-test.mjs <function-name>');
        })
        .catch(error => {
            console.error('❌ Quick test failed:', error);
            process.exit(1);
        });
}

export { runQuickTest };
