#!/usr/bin/env node

import fs from 'fs/promises';
import { createWriteStream } from 'fs';

/**
 * Metrics Analyzer for Load Test Results
 * Generates detailed performance insights and recommendations
 */

class MetricsAnalyzer {
    constructor(reportPath) {
        this.reportPath = reportPath;
        this.report = null;
    }

    async loadReport() {
        try {
            const data = await fs.readFile(this.reportPath, 'utf8');
            this.report = JSON.parse(data);
            return this.report;
        } catch (error) {
            throw new Error(`Failed to load report: ${error.message}`);
        }
    }

    analyzePerformanceMetrics() {
        if (!this.report || !this.report.results) {
            throw new Error('No report data available');
        }

        const results = this.report.results;
        const analysis = {
            performanceProfile: {},
            concurrencyAnalysis: {},
            memoryAnalysis: {},
            recommendations: []
        };

        // Performance Profile Analysis
        const allDurations = [];
        const successRates = [];
        
        results.forEach(result => {
            if (result.avgProcessingTime) {
                allDurations.push(parseFloat(result.avgProcessingTime));
            }
            if (result.successRate) {
                successRates.push(parseFloat(result.successRate));
            }
        });

        if (allDurations.length > 0) {
            analysis.performanceProfile = {
                avgProcessingTime: (allDurations.reduce((a, b) => a + b, 0) / allDurations.length).toFixed(2),
                minProcessingTime: Math.min(...allDurations).toFixed(2),
                maxProcessingTime: Math.max(...allDurations).toFixed(2),
                processingTimeVariance: this.calculateVariance(allDurations).toFixed(2),
                avgSuccessRate: (successRates.reduce((a, b) => a + b, 0) / successRates.length).toFixed(2)
            };
        }

        // Concurrency Analysis
        const concurrencyTests = results.filter(r => r.concurrencyLevel || r.testType?.includes('concurrent'));
        if (concurrencyTests.length > 0) {
            analysis.concurrencyAnalysis = this.analyzeConcurrency(concurrencyTests);
        }

        // Memory Analysis
        const memoryTests = results.filter(r => r.maxMemoryUsage && r.maxMemoryUsage !== 'N/A');
        if (memoryTests.length > 0) {
            analysis.memoryAnalysis = this.analyzeMemory(memoryTests);
        }

        // Generate Recommendations
        analysis.recommendations = this.generateRecommendations(analysis);

        return analysis;
    }

    analyzeConcurrency(concurrencyTests) {
        const analysis = {
            optimalConcurrency: null,
            scalabilityFactor: null,
            throughputAnalysis: {},
            concurrencyLimits: {}
        };

        // Find optimal concurrency (highest throughput with >95% success rate)
        const viableTests = concurrencyTests.filter(t => parseFloat(t.successRate) >= 95);
        if (viableTests.length > 0) {
            const optimal = viableTests.reduce((best, current) => {
                const currentThroughput = parseFloat(current.videosPerSecond || current.invocationsPerSecond || 0);
                const bestThroughput = parseFloat(best.videosPerSecond || best.invocationsPerSecond || 0);
                return currentThroughput > bestThroughput ? current : best;
            });
            
            analysis.optimalConcurrency = {
                level: optimal.concurrencyLevel || optimal.testType,
                throughput: optimal.videosPerSecond || optimal.invocationsPerSecond,
                successRate: optimal.successRate,
                avgProcessingTime: optimal.avgProcessingTime
            };
        }

        // Calculate scalability factor
        const sortedTests = concurrencyTests
            .filter(t => t.concurrencyLevel)
            .sort((a, b) => a.concurrencyLevel - b.concurrencyLevel);
        
        if (sortedTests.length >= 2) {
            const baseline = sortedTests[0];
            const highest = sortedTests[sortedTests.length - 1];
            
            const throughputIncrease = parseFloat(highest.videosPerSecond || highest.invocationsPerSecond || 0) / 
                                     parseFloat(baseline.videosPerSecond || baseline.invocationsPerSecond || 1);
            const concurrencyIncrease = highest.concurrencyLevel / baseline.concurrencyLevel;
            
            analysis.scalabilityFactor = (throughputIncrease / concurrencyIncrease).toFixed(2);
        }

        // Throughput analysis
        const throughputs = concurrencyTests.map(t => ({
            concurrency: t.concurrencyLevel,
            throughput: parseFloat(t.videosPerSecond || t.invocationsPerSecond || 0),
            successRate: parseFloat(t.successRate)
        }));

        analysis.throughputAnalysis = {
            maxThroughput: Math.max(...throughputs.map(t => t.throughput)).toFixed(2),
            throughputAtOptimalConcurrency: analysis.optimalConcurrency?.throughput || 'N/A',
            throughputDegradationPoint: this.findThroughputDegradationPoint(throughputs)
        };

        return analysis;
    }

    analyzeMemory(memoryTests) {
        const memoryUsages = memoryTests.map(t => parseFloat(t.maxMemoryUsage || t.maxMemoryUsed || 0));
        const avgMemoryUsages = memoryTests.map(t => parseFloat(t.avgMemoryUsage || t.avgMemoryUsed || 0)).filter(m => m > 0);

        return {
            peakMemoryUsage: Math.max(...memoryUsages).toFixed(2),
            avgMemoryUsage: avgMemoryUsages.length > 0 ? (avgMemoryUsages.reduce((a, b) => a + b, 0) / avgMemoryUsages.length).toFixed(2) : 'N/A',
            memoryEfficiency: ((10240 - Math.max(...memoryUsages)) / 10240 * 100).toFixed(1),
            memoryUtilization: (Math.max(...memoryUsages) / 10240 * 100).toFixed(1),
            memoryVariance: this.calculateVariance(memoryUsages).toFixed(2)
        };
    }

    generateRecommendations(analysis) {
        const recommendations = [];

        // Performance recommendations
        if (analysis.performanceProfile.avgProcessingTime) {
            const avgTime = parseFloat(analysis.performanceProfile.avgProcessingTime);
            
            if (avgTime > 45) {
                recommendations.push({
                    category: 'Performance',
                    priority: 'High',
                    issue: `Average processing time (${avgTime}s) exceeds 45s for 30s videos`,
                    recommendation: 'Consider optimizing FFmpeg settings: use "-preset fast" instead of "medium", or increase Lambda memory allocation'
                });
            } else if (avgTime < 15) {
                recommendations.push({
                    category: 'Performance',
                    priority: 'Low',
                    issue: 'Excellent performance detected',
                    recommendation: 'Consider reducing Lambda memory allocation to optimize costs while maintaining performance'
                });
            }
        }

        // Success rate recommendations
        if (analysis.performanceProfile.avgSuccessRate) {
            const successRate = parseFloat(analysis.performanceProfile.avgSuccessRate);
            
            if (successRate < 95) {
                recommendations.push({
                    category: 'Reliability',
                    priority: 'High',
                    issue: `Success rate (${successRate}%) below 95%`,
                    recommendation: 'Increase Lambda timeout from 180s to 300s, and consider increasing memory allocation'
                });
            }
        }

        // Concurrency recommendations
        if (analysis.concurrencyAnalysis.optimalConcurrency) {
            const optimal = analysis.concurrencyAnalysis.optimalConcurrency;
            recommendations.push({
                category: 'Concurrency',
                priority: 'Medium',
                issue: 'Optimal concurrency level identified',
                recommendation: `Set reserved concurrency to ${optimal.level} for optimal throughput (${optimal.throughput} videos/sec)`
            });
        }

        // Memory recommendations
        if (analysis.memoryAnalysis.memoryUtilization) {
            const utilization = parseFloat(analysis.memoryAnalysis.memoryUtilization);
            
            if (utilization > 80) {
                recommendations.push({
                    category: 'Memory',
                    priority: 'High',
                    issue: `High memory utilization (${utilization}%)`,
                    recommendation: 'Consider increasing Lambda memory allocation beyond 10GB'
                });
            } else if (utilization < 30) {
                recommendations.push({
                    category: 'Memory',
                    priority: 'Medium',
                    issue: `Low memory utilization (${utilization}%)`,
                    recommendation: 'Consider reducing Lambda memory allocation to 6-8GB to optimize costs'
                });
            }
        }

        // Scalability recommendations
        if (analysis.concurrencyAnalysis.scalabilityFactor) {
            const factor = parseFloat(analysis.concurrencyAnalysis.scalabilityFactor);
            
            if (factor < 0.7) {
                recommendations.push({
                    category: 'Scalability',
                    priority: 'Medium',
                    issue: `Poor scalability factor (${factor})`,
                    recommendation: 'Performance degrades significantly with increased concurrency. Consider implementing queue-based processing or optimizing resource usage'
                });
            }
        }

        return recommendations;
    }

    calculateVariance(numbers) {
        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
        return Math.sqrt(variance);
    }

    findThroughputDegradationPoint(throughputs) {
        const sorted = throughputs.sort((a, b) => a.concurrency - b.concurrency);
        
        for (let i = 1; i < sorted.length; i++) {
            if (sorted[i].throughput < sorted[i-1].throughput * 0.9) {
                return sorted[i].concurrency;
            }
        }
        
        return null;
    }

    async generateDetailedReport() {
        const analysis = this.analyzePerformanceMetrics();
        
        const detailedReport = {
            ...this.report,
            analysis,
            generatedAt: new Date().toISOString()
        };

        // Save enhanced report
        await fs.writeFile(
            'detailed-metrics-analysis.json',
            JSON.stringify(detailedReport, null, 2)
        );

        return detailedReport;
    }

    async generateHtmlReport() {
        const analysis = this.analyzePerformanceMetrics();
        
        const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Video Poster Generation Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f9f9f9; border-radius: 3px; }
        .recommendation { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f0f8ff; }
        .high-priority { border-left-color: #d32f2f; background: #ffebee; }
        .medium-priority { border-left-color: #f57c00; background: #fff3e0; }
        .low-priority { border-left-color: #388e3c; background: #e8f5e8; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Video Poster Generation Load Test Report</h1>
        <p>Generated: ${new Date().toISOString()}</p>
        <p>Test Suite: ${this.report.testSuite || 'Load Test'}</p>
    </div>

    <div class="section">
        <h2>Performance Summary</h2>
        <div class="metric">
            <strong>Average Processing Time:</strong> ${analysis.performanceProfile.avgProcessingTime || 'N/A'}s
        </div>
        <div class="metric">
            <strong>Success Rate:</strong> ${analysis.performanceProfile.avgSuccessRate || 'N/A'}%
        </div>
        <div class="metric">
            <strong>Peak Memory Usage:</strong> ${analysis.memoryAnalysis.peakMemoryUsage || 'N/A'}MB
        </div>
        <div class="metric">
            <strong>Optimal Concurrency:</strong> ${analysis.concurrencyAnalysis.optimalConcurrency?.level || 'N/A'}
        </div>
    </div>

    <div class="section">
        <h2>Detailed Test Results</h2>
        <table>
            <tr>
                <th>Test</th>
                <th>Success Rate</th>
                <th>Avg Time (s)</th>
                <th>Throughput</th>
                <th>Memory (MB)</th>
            </tr>
            ${this.report.results.map(result => `
                <tr>
                    <td>${result.testType || result.testName}</td>
                    <td>${result.successRate || 'N/A'}%</td>
                    <td>${result.avgProcessingTime || 'N/A'}</td>
                    <td>${result.videosPerSecond || result.invocationsPerSecond || 'N/A'}</td>
                    <td>${result.maxMemoryUsage || result.maxMemoryUsed || 'N/A'}</td>
                </tr>
            `).join('')}
        </table>
    </div>

    <div class="section">
        <h2>Recommendations</h2>
        ${analysis.recommendations.map(rec => `
            <div class="recommendation ${rec.priority.toLowerCase()}-priority">
                <strong>${rec.category} (${rec.priority} Priority):</strong> ${rec.issue}<br>
                <em>Recommendation:</em> ${rec.recommendation}
            </div>
        `).join('')}
    </div>
</body>
</html>`;

        await fs.writeFile('load-test-report.html', html);
        return 'load-test-report.html';
    }
}

async function analyzeReport(reportPath) {
    console.log('📊 Analyzing load test metrics...');
    
    const analyzer = new MetricsAnalyzer(reportPath);
    await analyzer.loadReport();
    
    const detailedReport = await analyzer.generateDetailedReport();
    const htmlReportPath = await analyzer.generateHtmlReport();
    
    console.log('✅ Analysis complete!');
    console.log(`📄 Detailed JSON report: detailed-metrics-analysis.json`);
    console.log(`🌐 HTML report: ${htmlReportPath}`);
    
    return detailedReport;
}

export { MetricsAnalyzer, analyzeReport };

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
    const reportPath = process.argv[2] || 'load-test-report.json';
    
    analyzeReport(reportPath)
        .then(() => console.log('🎉 Metrics analysis completed!'))
        .catch(error => {
            console.error('❌ Analysis failed:', error);
            process.exit(1);
        });
}
