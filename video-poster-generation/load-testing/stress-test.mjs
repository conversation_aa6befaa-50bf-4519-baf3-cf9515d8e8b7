#!/usr/bin/env node

import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';
import { CloudWatchClient, GetMetricStatisticsCommand } from '@aws-sdk/client-cloudwatch';
import fs from 'fs/promises';

/**
 * Stress Testing Suite - Find Maximum Load Capacity
 * Progressively increases load until failure point is found
 */

class StressTester {
    constructor(functionName, region = 'ap-south-1') {
        this.functionName = functionName;
        this.region = region;
        this.lambdaClient = new LambdaClient({ region });
        this.cloudWatchClient = new CloudWatchClient({ region });
        this.results = [];
        this.maxConcurrencyFound = 0;
        this.breakingPoint = null;
        this.samplePayload = null;
    }

    async loadSamplePayload() {
        try {
            const data = await fs.readFile('sample-payload.json', 'utf8');
            this.samplePayload = JSON.parse(data);

            // Analyze the payload structure
            const videoElements = this.samplePayload.payload.filter(p => p.type === 'video');
            const photoElements = this.samplePayload.payload.filter(p => p.type === 'photo');

            console.log('✅ Loaded sample payload with full video composition:');
            console.log(`   📐 Frame: ${this.samplePayload.frame_width}x${this.samplePayload.frame_height}`);
            console.log(`   🎬 Videos: ${videoElements.length} (with borders: ${videoElements.filter(v => v.border).length})`);
            console.log(`   📷 Photos: ${photoElements.length}`);
            console.log(`   🎯 Total elements: ${this.samplePayload.payload.length}`);

            if (videoElements.length > 0) {
                console.log(`   🎥 Video URL: ${videoElements[0].url}`);
                if (videoElements[0].border) {
                    console.log(`   🖼️  Border: radius=${videoElements[0].border.radius}, width=${videoElements[0].border.width}, color=${videoElements[0].border.color}`);
                }
            }

            return this.samplePayload;
        } catch (error) {
            console.error('❌ Failed to load sample-payload.json:', error.message);
            throw new Error('Cannot proceed without sample payload');
        }
    }

    generateUniquePayload(testId) {
        if (!this.samplePayload) {
            throw new Error('Sample payload not loaded');
        }

        // Create a deep copy of the sample payload
        const payload = JSON.parse(JSON.stringify(this.samplePayload));

        // Generate unique job ID using Date.now() as requested
        const uniqueJobId = `${Date.now()}`;
        payload.job_id = uniqueJobId;

        // Use test callback URL
        payload.callback_url = "https://httpbin.org/anything";

        // Keep the exact same format as sample-payload.json (including nested border structure)
        console.log(`📋 Generated payload for ${testId} with job_id: ${uniqueJobId}`);
        console.log(`🎬 Video element: ${payload.payload.find(p => p.type === 'video')?.url || 'No video found'}`);

        return payload;
    }

    async invokeLambda(payload, testId) {
        const startTime = Date.now();

        try {
            const command = new InvokeCommand({
                FunctionName: this.functionName,
                Payload: JSON.stringify(payload),
                InvocationType: 'RequestResponse'
            });

            const response = await this.lambdaClient.send(command);
            const endTime = Date.now();

            const duration = endTime - startTime;
            const responsePayload = JSON.parse(new TextDecoder().decode(response.Payload));

            if (response.StatusCode === 200 && !responsePayload.errorMessage) {
                return {
                    testId,
                    duration: duration / 1000,
                    success: true,
                    statusCode: response.StatusCode,
                    response: responsePayload,
                    timestamp: new Date().toISOString()
                };
            } else {
                throw new Error(responsePayload.errorMessage || `Lambda failed with status ${response.StatusCode}`);
            }

        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            return {
                testId,
                duration: duration / 1000,
                success: false,
                error: error.message,
                errorType: this.categorizeError(error.message),
                timestamp: new Date().toISOString()
            };
        }
    }

    categorizeError(errorMessage) {
        if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
            return 'TIMEOUT';
        } else if (errorMessage.includes('throttle') || errorMessage.includes('Throttle')) {
            return 'THROTTLING';
        } else if (errorMessage.includes('memory') || errorMessage.includes('Memory')) {
            return 'MEMORY';
        } else if (errorMessage.includes('concurrent') || errorMessage.includes('Concurrent')) {
            return 'CONCURRENCY_LIMIT';
        } else {
            return 'OTHER';
        }
    }

    async runStressTest(concurrency, testName, iterations = 1) {
        console.log(`\n🔥 STRESS TEST: ${concurrency} concurrent executions (${iterations} iterations)`);
        console.log('='.repeat(60));

        const allResults = [];
        let totalSuccessful = 0;
        let totalFailed = 0;

        for (let iteration = 1; iteration <= iterations; iteration++) {
            console.log(`\n📊 Iteration ${iteration}/${iterations} - ${concurrency} concurrent invocations...`);

            const startTime = Date.now();
            const promises = [];

            for (let i = 0; i < concurrency; i++) {
                const testPayload = this.generateUniquePayload(`${testName}-${iteration}-${i + 1}`);
                promises.push(this.invokeLambda(testPayload, `${testName}-${iteration}-${i + 1}`));
            }

            const results = await Promise.allSettled(promises);
            const endTime = Date.now();

            const successful = [];
            const failed = [];

            results.forEach(result => {
                if (result.status === 'fulfilled') {
                    if (result.value.success) {
                        successful.push(result.value);
                        totalSuccessful++;
                    } else {
                        failed.push(result.value);
                        totalFailed++;
                    }
                } else {
                    failed.push({
                        error: result.reason.message,
                        errorType: 'PROMISE_REJECTION',
                        timestamp: new Date().toISOString()
                    });
                    totalFailed++;
                }
            });

            allResults.push(...successful, ...failed);

            const iterationDuration = (endTime - startTime) / 1000;
            const successRate = (successful.length / concurrency * 100).toFixed(2);
            const avgDuration = successful.length > 0 ?
                (successful.reduce((sum, r) => sum + r.duration, 0) / successful.length).toFixed(2) : 'N/A';

            console.log(`   ✅ Successful: ${successful.length}/${concurrency} (${successRate}%)`);
            console.log(`   ❌ Failed: ${failed.length}/${concurrency}`);
            console.log(`   ⏱️  Avg Duration: ${avgDuration}s`);
            console.log(`   🕐 Wall Clock: ${iterationDuration.toFixed(2)}s`);

            if (failed.length > 0) {
                const errorTypes = {};
                failed.forEach(f => {
                    const type = f.errorType || 'UNKNOWN';
                    errorTypes[type] = (errorTypes[type] || 0) + 1;
                });
                console.log(`   🚨 Error Types:`, errorTypes);
            }

            // Wait between iterations to avoid overwhelming the system
            if (iteration < iterations) {
                console.log(`   ⏳ Waiting 5s before next iteration...`);
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }

        const overallSuccessRate = (totalSuccessful / (totalSuccessful + totalFailed) * 100).toFixed(2);
        const successful = allResults.filter(r => r.success);
        const avgProcessingTime = successful.length > 0 ?
            (successful.reduce((sum, r) => sum + r.duration, 0) / successful.length).toFixed(2) : 'N/A';

        const result = {
            testName,
            concurrency,
            iterations,
            totalTests: totalSuccessful + totalFailed,
            totalSuccessful,
            totalFailed,
            successRate: overallSuccessRate,
            avgProcessingTime,
            results: allResults,
            isPassing: parseFloat(overallSuccessRate) >= 90 // 90% threshold for stress testing
        };

        console.log(`\n📊 STRESS TEST SUMMARY (${concurrency} concurrent):`);
        console.log(`   Overall Success Rate: ${overallSuccessRate}%`);
        console.log(`   Average Processing Time: ${avgProcessingTime}s`);
        console.log(`   Status: ${result.isPassing ? '✅ PASSING' : '❌ FAILING'}`);

        return result;
    }

    async findMaximumLoad() {
        console.log('🎯 FINDING MAXIMUM LOAD CAPACITY');
        console.log('================================');
        console.log('Testing progressive concurrency levels until failure...\n');

        // Progressive concurrency levels - start from known good point
        const concurrencyLevels = [15, 20, 25, 30, 40, 50, 75, 100, 150, 200];
        let lastSuccessfulLevel = 10; // We know 10 works from previous tests

        for (const concurrency of concurrencyLevels) {
            console.log(`\n🚀 Testing ${concurrency} concurrent executions...`);

            const result = await this.runStressTest(concurrency, `max-load-${concurrency}`, 2);
            this.results.push(result);

            if (result.isPassing) {
                lastSuccessfulLevel = concurrency;
                this.maxConcurrencyFound = concurrency;
                console.log(`✅ ${concurrency} concurrent: PASSED (${result.successRate}% success)`);
            } else {
                this.breakingPoint = {
                    concurrency,
                    successRate: result.successRate,
                    errors: result.results.filter(r => !r.success)
                };
                console.log(`❌ ${concurrency} concurrent: FAILED (${result.successRate}% success)`);
                console.log(`🎯 Maximum capacity found: ${lastSuccessfulLevel} concurrent executions`);
                break;
            }

            // Add longer delay for higher concurrency tests
            const delay = concurrency > 50 ? 15000 : 10000;
            console.log(`⏳ Waiting ${delay / 1000}s before next test...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }

        return {
            maxConcurrency: this.maxConcurrencyFound,
            breakingPoint: this.breakingPoint,
            allResults: this.results
        };
    }

    async runSustainedLoadTest(concurrency, duration = 300) {
        console.log(`\n🔄 SUSTAINED LOAD TEST`);
        console.log(`Testing ${concurrency} concurrent executions for ${duration} seconds...`);

        const startTime = Date.now();
        const endTime = startTime + (duration * 1000);
        const results = [];
        let iteration = 1;

        while (Date.now() < endTime) {
            const remaining = Math.ceil((endTime - Date.now()) / 1000);
            console.log(`\n⏱️  Sustained test iteration ${iteration} (${remaining}s remaining)...`);

            const result = await this.runStressTest(concurrency, `sustained-${iteration}`, 1);
            results.push(result);

            if (!result.isPassing) {
                console.log(`❌ Sustained load test failed at iteration ${iteration}`);
                break;
            }

            iteration++;

            // Short delay between sustained iterations
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        const totalTests = results.reduce((sum, r) => sum + r.totalTests, 0);
        const totalSuccessful = results.reduce((sum, r) => sum + r.totalSuccessful, 0);
        const overallSuccessRate = (totalSuccessful / totalTests * 100).toFixed(2);

        console.log(`\n📊 SUSTAINED LOAD SUMMARY:`);
        console.log(`   Duration: ${((Date.now() - startTime) / 1000).toFixed(0)}s`);
        console.log(`   Iterations: ${results.length}`);
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   Success Rate: ${overallSuccessRate}%`);

        return {
            concurrency,
            duration: (Date.now() - startTime) / 1000,
            iterations: results.length,
            totalTests,
            totalSuccessful,
            successRate: overallSuccessRate,
            results
        };
    }

    async generateStressReport() {
        const report = {
            functionName: this.functionName,
            testSuite: 'Stress Testing Suite',
            timestamp: new Date().toISOString(),
            maxConcurrencyFound: this.maxConcurrencyFound,
            breakingPoint: this.breakingPoint,
            results: this.results,
            recommendations: this.generateRecommendations()
        };

        await fs.writeFile('stress-test-report.json', JSON.stringify(report, null, 2));
        return report;
    }

    generateRecommendations() {
        const recommendations = [];

        if (this.maxConcurrencyFound >= 50) {
            recommendations.push('🚀 Excellent scalability - can handle 50+ concurrent executions');
        } else if (this.maxConcurrencyFound >= 25) {
            recommendations.push('✅ Good scalability - can handle 25+ concurrent executions');
        } else {
            recommendations.push('⚠️ Limited scalability - consider optimizing for higher concurrency');
        }

        if (this.breakingPoint) {
            const errorTypes = {};
            this.breakingPoint.errors.forEach(e => {
                const type = e.errorType || 'UNKNOWN';
                errorTypes[type] = (errorTypes[type] || 0) + 1;
            });

            const primaryError = Object.keys(errorTypes).reduce((a, b) =>
                errorTypes[a] > errorTypes[b] ? a : b
            );

            switch (primaryError) {
                case 'TIMEOUT':
                    recommendations.push('⏰ Primary limitation: Timeouts - consider increasing timeout or optimizing processing');
                    break;
                case 'THROTTLING':
                    recommendations.push('🚦 Primary limitation: AWS throttling - consider reserved concurrency or account limits');
                    break;
                case 'MEMORY':
                    recommendations.push('🧠 Primary limitation: Memory - consider increasing memory allocation');
                    break;
                case 'CONCURRENCY_LIMIT':
                    recommendations.push('🔢 Primary limitation: Concurrency limits - check AWS account limits');
                    break;
                default:
                    recommendations.push('🔍 Mixed error types - review detailed error logs for optimization opportunities');
            }
        }

        recommendations.push(`💡 Recommended production concurrency: ${Math.floor(this.maxConcurrencyFound * 0.8)} (80% of max capacity)`);

        return recommendations;
    }
}

async function runStressTests(functionName) {
    console.log('🔥 LAMBDA STRESS TESTING SUITE');
    console.log('==============================');
    console.log(`Function: ${functionName}`);
    console.log('Objective: Find maximum load capacity\n');

    const tester = new StressTester(functionName);

    try {
        // Load sample payload first
        await tester.loadSamplePayload();

        // Phase 1: Find maximum concurrency
        console.log('🎯 PHASE 1: Maximum Concurrency Discovery');
        const maxLoadResult = await tester.findMaximumLoad();

        // Phase 2: Sustained load test at 80% of max capacity
        if (maxLoadResult.maxConcurrency > 0) {
            const sustainedConcurrency = Math.floor(maxLoadResult.maxConcurrency * 0.8);
            console.log(`\n🔄 PHASE 2: Sustained Load Test at ${sustainedConcurrency} concurrent`);
            const sustainedResult = await tester.runSustainedLoadTest(sustainedConcurrency, 180); // 3 minutes

            maxLoadResult.sustainedLoadTest = sustainedResult;
        }

        // Generate comprehensive report
        const report = await tester.generateStressReport();

        console.log('\n🎉 STRESS TESTING COMPLETE!');
        console.log('============================');
        console.log(`📊 Maximum Concurrency: ${maxLoadResult.maxConcurrency}`);
        console.log(`📄 Report saved to: stress-test-report.json`);

        if (maxLoadResult.breakingPoint) {
            console.log(`⚠️  Breaking Point: ${maxLoadResult.breakingPoint.concurrency} concurrent (${maxLoadResult.breakingPoint.successRate}% success)`);
        }

        console.log('\n💡 RECOMMENDATIONS:');
        report.recommendations.forEach(rec => console.log(`   ${rec}`));

        return report;

    } catch (error) {
        console.error('❌ Stress testing failed:', error);
        throw error;
    }
}

export { StressTester, runStressTests };

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
    const functionName = process.argv[2];
    if (!functionName) {
        console.error('Usage: node stress-test.mjs <function-name>');
        process.exit(1);
    }

    runStressTests(functionName)
        .then(() => console.log('🎉 Stress testing completed successfully!'))
        .catch(error => {
            console.error('❌ Stress testing failed:', error);
            process.exit(1);
        });
}
