# Video Poster Generation Lambda

This AWS Lambda function creates video compositions by stacking media elements (photos and videos) based on their z-order in the payload array.

## Features

- **Media Stacking**: Stack photos and videos in layers based on array order
- **Flexible Positioning**: Position each element with custom x, y coordinates
- **Custom Sizing**: Specify width and height for each media element
- **Video Support**: Handle both photos and videos in the composition
- **Video Borders**: Customizable borders with radius, size, and color for video elements
- **Audio Preservation**: Automatically extracts and preserves audio from the first video with audio
- **Video Metadata**: Returns comprehensive metadata including width, height, duration, and bitrate
- **Rounded Corners**: Apply border radius to create rounded corners on video elements
- **Error Handling**: Comprehensive error handling with callback notifications
- **S3 Integration**: Automatic upload of generated videos and thumbnails

## Input Payload Structure

```json
{
    "payload": [
        {
            "url": "https://example.com/photo1.png",
            "width": 720,
            "height": 135,
            "x": 0,
            "y": 0,
            "type": "photo"
        },
        {
            "url": "https://example.com/video1.mp4",
            "width": 720,
            "height": 135,
            "x": 0,
            "y": 135,
            "type": "video",
            "border": {
                "radius": 20,
                "width": 5,
                "color": "#ff0000"
            }
        }
    ],
    "frame_height": 1280,
    "frame_width": 720,
    "job_id": "unique-job-id",
    "callback_url": "https://your-callback-endpoint.com"
}
```

## Payload Fields

### Root Level
- `payload`: Array of media elements to stack (required)
- `frame_width`: Width of the output video canvas (required)
- `frame_height`: Height of the output video canvas (required)
- `job_id`: Unique identifier for the job (required)
- `callback_url`: URL for status callbacks (required)

### Media Element Fields
- `url`: URL of the media file (photo or video) (required)
- `width`: Width to scale the media to (required)
- `height`: Height to scale the media to (required)
- `x`: X position on the canvas (required)
- `y`: Y position on the canvas (required)
- `type`: Either "photo" or "video" (required)
- `border`: Border object containing radius, width, and color properties (required for videos)
  - `radius`: Border radius in pixels for rounded corners
  - `width`: Border thickness in pixels
  - `color`: Border color as hex (#ff0000), rgb(255,0,0), or named color (red)

## Output

The function generates:
1. A composed video file uploaded to S3 with preserved audio from the first video
2. A thumbnail image uploaded to S3
3. Comprehensive video metadata including:
   - `width`: Output video width in pixels
   - `height`: Output video height in pixels
   - `duration`: Video duration in seconds
   - `bitrate`: Video bitrate in bits per second
4. Callback notifications for success/failure

### Response Format

```json
{
  "statusCode": 200,
  "body": {
    "video_url": "https://bucket.s3.region.amazonaws.com/video.mp4",
    "thumbnail_url": "https://bucket.s3.region.amazonaws.com/thumbnail.jpg",
    "job_id": "unique-job-identifier",
    "width": 1920,
    "height": 1080,
    "duration": 15.5,
    "bitrate": 2500000
  }
}
```

## Error Handling

The function includes comprehensive error handling for:
- Invalid input validation
- Media download failures
- Video generation failures
- Upload failures
- Process timeouts (5 minutes)

## Dependencies

- `fluent-ffmpeg`: Video processing
- `axios`: HTTP requests and callbacks
- `@aws-sdk/client-s3`: S3 uploads
- Standard Node.js modules for file operations

## Deployment

This function is designed to be deployed as an AWS Lambda using AWS SAM. See `template.yaml` for deployment configuration.

## Notes

- Media elements are stacked in the order they appear in the payload array
- The first element is at the bottom (z-index 0), subsequent elements stack on top
- Video elements require border object with radius, width, and color properties
- Border creates a colored frame around videos with customizable thickness and rounded corners
- Default video duration is 10 seconds for videos, 5 seconds for photo-only compositions
