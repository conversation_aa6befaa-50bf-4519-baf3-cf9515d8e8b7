import { promisify } from "util";
import { getExtensionFromContentType, extractExtensionFromUrl } from "./extensions-utilities.mjs";
import { tmpdir } from "os";
import { join } from "path";
import { ulid } from "ulid";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import ffmpeg from "fluent-ffmpeg";
import fs from "fs";
import axios from 'axios';
import { ERROR_CODES, sendErrorCallback } from "./error-handling-utilities.mjs";
import { mkdtemp as mkdtempCallback } from 'fs';
import { rm } from 'fs/promises';
import { mediaCache } from './media-cache.mjs';

const mkdtemp = promisify(mkdtempCallback);

export const removeFolderRecursively = async (job_id) => {
    await rm(join(tmpdir(), job_id), { recursive: true, force: true });
}
export const downloadMedia = async (mediaUrl, job_id, callback_url) => {
    try {
        // Check cache first
        const cachedPath = await mediaCache.get(mediaUrl);
        if (cachedPath) {
            // Copy cached file to job-specific temp directory for processing
            const uniqueId = ulid();
            const systemTmpDir = tmpdir();
            const tempDir = await mkdtemp(join(systemTmpDir, job_id));

            // Determine extension from cached file
            const extension = cachedPath.substring(cachedPath.lastIndexOf('.'));
            const fileName = uniqueId + extension;
            const outputPath = join(tempDir, fileName);

            // Copy from cache to temp directory
            await fs.promises.copyFile(cachedPath, outputPath);
            return outputPath;
        }

        // Cache miss - download the file
        const uniqueId = ulid();
        const systemTmpDir = tmpdir();
        const tempDir = await mkdtemp(join(systemTmpDir, job_id));

        const response = await axios({
            url: mediaUrl,
            method: 'GET',
            responseType: 'stream'
        });

        const contentType = response.headers['content-type'];
        let extension = getExtensionFromContentType(contentType) || extractExtensionFromUrl(mediaUrl);
        if (!extension) throw new Error(`Unable to determine file extension for ${mediaUrl}`);

        const fileName = uniqueId + extension;
        const outputPath = join(tempDir, fileName);
        const writer = fs.createWriteStream(outputPath);

        response.data.pipe(writer);

        return new Promise((resolve, reject) => {
            writer.on('finish', async () => {
                try {
                    // Store in cache after successful download
                    await mediaCache.set(mediaUrl, outputPath, extension);
                    resolve(outputPath);
                } catch (cacheError) {
                    // Don't fail the download if caching fails
                    console.warn('Failed to cache downloaded media:', cacheError.message);
                    resolve(outputPath);
                }
            });
            writer.on('error', reject);
        });
    } catch (err) {
        await sendErrorCallback(job_id, ERROR_CODES.DOWNLOAD_FAILED, 'Failed to download media', callback_url);
        throw new Error(ERROR_CODES.DOWNLOAD_FAILED);
    }
};

export const videoSizeOf = async (video, job_id, callback_url) => {
    return new Promise((resolve, reject) => {
        ffmpeg(video).ffprobe(0, (err, data) => {
            if (err) {
                sendErrorCallback(job_id, ERROR_CODES.VIDEO_GENERATION_FAILED, 'Failed to get video size', callback_url);
                reject(new Error(ERROR_CODES.VIDEO_GENERATION_FAILED));
            }
            resolve({ width: data.streams[0].width, height: data.streams[0].height });
        });
    });
};

/**
 * Extract comprehensive video metadata including duration, bitrate, dimensions
 */
export const getVideoMetadata = async (videoPath, job_id, callback_url) => {
    return new Promise((resolve, reject) => {
        ffmpeg(videoPath).ffprobe(0, (err, data) => {
            if (err) {
                sendErrorCallback(job_id, ERROR_CODES.VIDEO_GENERATION_FAILED, 'Failed to get video metadata', callback_url);
                reject(new Error(ERROR_CODES.VIDEO_GENERATION_FAILED));
            }

            try {
                const videoStream = data.streams.find(stream => stream.codec_type === 'video');
                const audioStream = data.streams.find(stream => stream.codec_type === 'audio');

                if (!videoStream) {
                    throw new Error('No video stream found in file');
                }

                const metadata = {
                    width: videoStream.width,
                    height: videoStream.height,
                    duration: parseFloat(data.format.duration) || 0,
                    bitrate: parseInt(data.format.bit_rate) || 0,
                    hasAudio: !!audioStream
                };

                resolve(metadata);
            } catch (parseError) {
                sendErrorCallback(job_id, ERROR_CODES.VIDEO_GENERATION_FAILED, `Failed to parse video metadata: ${parseError.message}`, callback_url);
                reject(new Error(ERROR_CODES.VIDEO_GENERATION_FAILED));
            }
        });
    });
};

export const generateThumbnail = async (job_id, videoPath, bucketName, s3, callback_url) => {
    const thumbnailFilename = `${job_id}-thumbnail.jpg`;
    const thumbnailPath = join(tmpdir(), thumbnailFilename);

    const ffmpegPromise = promisify((callback) => {
        ffmpeg(videoPath)
            .screenshots({
                count: 1,
                filename: thumbnailFilename,
                folder: tmpdir(),
            })
            .on('end', () => callback(null))
            .on('error', (err) => callback(err));
    });

    try {
        await ffmpegPromise();
    } catch (err) {
        await sendErrorCallback(job_id, ERROR_CODES.THUMBNAIL_GENERATION_FAILED, 'Failed to generate thumbnail', callback_url);
        throw new Error(ERROR_CODES.THUMBNAIL_GENERATION_FAILED);
    }

    const uploadParams = {
        Bucket: bucketName,
        Key: `production/video-posters/${thumbnailFilename}`,
        Body: fs.createReadStream(thumbnailPath)
    };

    try {
        await s3.send(new PutObjectCommand(uploadParams));
    } catch (err) {
        await sendErrorCallback(job_id, ERROR_CODES.UPLOAD_FAILED, 'Failed to upload thumbnail', callback_url);
        throw new Error(ERROR_CODES.UPLOAD_FAILED);
    }

    return thumbnailFilename;
};

export const uploadToS3 = async (filePath, bucketName, fileName, job_id, callback_url) => {
    const s3 = new S3Client({ region: 'ap-south-1' });
    const uploadParams = {
        Bucket: bucketName,
        Key: `video-posters/${fileName}`,
        Body: fs.createReadStream(filePath)
    };

    try {
        await s3.send(new PutObjectCommand(uploadParams));
    } catch (err) {
        await sendErrorCallback(job_id, ERROR_CODES.UPLOAD_FAILED, 'Failed to upload video', callback_url);
        throw new Error(ERROR_CODES.UPLOAD_FAILED);
    }
};

/**
 * Get media cache statistics and management functions
 */
export const getCacheStats = () => {
    return mediaCache.getStats();
};

export const clearMediaCache = async () => {
    await mediaCache.clear();
};

export const cleanupExpiredCache = async () => {
    await mediaCache.cleanupExpired();
};
