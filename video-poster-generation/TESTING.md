# Testing Guide for Video Poster Generation Lambda

## Local Testing

### Prerequisites
- Node.js 20.x
- FFmpeg installed locally (for basic testing)
- AWS CLI configured (for S3 uploads)
- SAM CLI installed

### Setup
```bash
cd video-poster-generation
npm install
```

### Validation Testing
Test the input validation logic:
```bash
node test-validation.mjs
```

Expected output:
```
✅ Valid event passed validation
✅ Invalid event 1 correctly failed validation
✅ Invalid event 2 correctly failed validation
```

### Handler Testing
Test the complete handler logic:
```bash
node test-handler.mjs
```

Expected output:
```
✅ Handler completed successfully: {
  statusCode: 200,
  body: '{"video_url":"https://praja-raw-user-videos.s3.ap-south-1.amazonaws.com/...","thumbnailUrl":"https://circle-app-photos.s3.ap-south-1.amazonaws.com/..."}'
}
```

### SAM CLI Testing
Test with SAM CLI (simulates Lambda environment):
```bash
sam build
sam local invoke VideoPosterGeneration --event test-event.json
```

Expected output:
```
{"statusCode": 200, "body": "{\"video_url\":\"https://praja-raw-user-videos.s3.ap-south-1.amazonaws.com/...\",\"thumbnailUrl\":\"https://circle-app-photos.s3.ap-south-1.amazonaws.com/...\"}"}
```

## Test Payloads

### Basic Photo Stacking
```json
{
    "payload": [
        {
            "url": "https://httpbin.org/image/png",
            "width": 360,
            "height": 200,
            "x": 0,
            "y": 0,
            "type": "photo"
        },
        {
            "url": "https://httpbin.org/image/jpeg",
            "width": 180,
            "height": 180,
            "x": 90,
            "y": 250,
            "type": "photo"
        }
    ],
    "frame_height": 720,
    "frame_width": 360,
    "job_id": "test-job-123",
    "callback_url": "https://httpbin.org/anything"
}
```

### Video with Border Radius
```json
{
    "payload": [
        {
            "url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
            "width": 720,
            "height": 405,
            "x": 0,
            "y": 0,
            "type": "video",
            "border": {
                "radius": 20,
                "width": 5,
                "color": "#000000"
            }
        }
    ],
    "frame_height": 1280,
    "frame_width": 720,
    "job_id": "test-video-123",
    "callback_url": "https://your-callback-url.com"
}
```

## Deployment Testing

### Validate Template
```bash
sam validate --lint
```

### Deploy to AWS
```bash
sam deploy --guided
```

### Test Deployed Function
```bash
aws lambda invoke \
  --function-name video-poster-generation-VideoPosterGeneration-XXXXX \
  --payload file://test-event.json \
  response.json
```

## Performance Notes

- Local testing: ~2-5 seconds for simple compositions
- Lambda environment: ~8-15 seconds depending on media size and count
- Memory usage: ~500MB-2GB depending on media resolution
- Timeout: Set to 180 seconds (3 minutes) to handle complex compositions

## Troubleshooting

### Common Issues

1. **FFmpeg not found locally**: Install FFmpeg or use SAM CLI for testing
2. **S3 upload failures**: Check AWS credentials and bucket permissions
3. **Media download failures**: Verify URLs are accessible and return valid media
4. **Timeout errors**: Increase timeout for large media files or complex compositions

### Debug Mode
Set environment variable for verbose logging:
```bash
export DEBUG=1
node test-handler.mjs
```
