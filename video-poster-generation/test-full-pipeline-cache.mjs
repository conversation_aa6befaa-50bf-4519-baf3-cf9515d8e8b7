#!/usr/bin/env node

import { handler } from './index.mjs';
import { getCacheStats, clearMediaCache } from './general-video-processing-utilities.mjs';
import { ulid } from 'ulid';

/**
 * Test full video generation pipeline with caching
 */

async function testFullPipelineWithCache() {
    console.log('🎬 Full Pipeline Cache Test');
    console.log('===========================');

    const testPayload = {
        elements: [
            {
                url: "https://a-cdn.thecircleapp.in/production/user-protocol-photos/sc.png",
                width: 720,
                height: 200,
                x: 0,
                y: 0,
                type: "photo"
            },
            {
                url: "https://a-cdn.thecircleapp.in/production/photos/133280/b2e78dee-4b08-4448-9909-e3eb8a7df991.jpg",
                width: 200,
                height: 150,
                x: 500,
                y: 350,
                type: "photo"
            }
        ],
        frame_height: 720,
        frame_width: 720,
        scaling_factor: 1.0,
        job_id: ulid(),
        callback_url: 'https://example.com/callback'
    };

    try {
        // Clear cache
        console.log('\n1. Clearing cache...');
        await clearMediaCache();
        
        console.log('Initial cache stats:', getCacheStats());

        // First generation - cache miss
        console.log('\n2. First video generation (cache miss expected)...');
        const startTime1 = Date.now();
        
        const result1 = await handler(testPayload);
        const duration1 = Date.now() - startTime1;
        
        console.log(`✅ First generation completed in ${duration1}ms`);
        console.log('Result:', JSON.parse(result1.body));
        
        const stats1 = getCacheStats();
        console.log('Cache after first generation:', {
            totalEntries: stats1.totalEntries,
            validEntries: stats1.validEntries,
            totalSizeMB: stats1.totalSizeMB
        });

        // Second generation with same URLs - cache hit
        console.log('\n3. Second video generation (cache hit expected)...');
        const testPayload2 = {
            ...testPayload,
            job_id: ulid() // Different job ID
        };
        
        const startTime2 = Date.now();
        const result2 = await handler(testPayload2);
        const duration2 = Date.now() - startTime2;
        
        console.log(`✅ Second generation completed in ${duration2}ms`);
        console.log('Result:', JSON.parse(result2.body));
        
        const stats2 = getCacheStats();
        console.log('Cache after second generation:', {
            totalEntries: stats2.totalEntries,
            validEntries: stats2.validEntries,
            totalSizeMB: stats2.totalSizeMB
        });

        // Performance comparison
        const speedup = duration1 > duration2 ? ((duration1 - duration2) / duration1 * 100).toFixed(1) : 0;
        console.log(`\n📊 Performance Comparison:`);
        console.log(`First generation: ${duration1}ms`);
        console.log(`Second generation: ${duration2}ms`);
        if (speedup > 0) {
            console.log(`🚀 Cache speedup: ${speedup}% faster`);
        }

        console.log('\n🎉 Full pipeline cache test completed!');
        
        return { duration1, duration2, speedup, cacheStats: stats2 };

    } catch (error) {
        console.error('❌ Full pipeline test failed:', error.message);
        throw error;
    }
}

// Run test
if (import.meta.url === `file://${process.argv[1]}`) {
    testFullPipelineWithCache().catch(console.error);
}

export { testFullPipelineWithCache };
