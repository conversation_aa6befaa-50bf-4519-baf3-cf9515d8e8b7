// Helper functions
export const getExtensionFromContentType = (contentType) => {
    const mime = {
        'video/mp4': '.mp4',
        'video/mpeg': '.mpeg',
        'video/ogg': '.ogv',
        'video/webm': '.webm',
        'video/quicktime': '.mov',
        'video/x-matroska': '.mkv',
        'image/jpeg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'image/bmp': '.bmp',
        'image/tiff': '.tiff',
        'image/svg+xml': '.svg'
    };
    return mime[contentType] || '';
};

export const extractExtensionFromUrl = (url) => '.' + url.split(/[#?]/)[0].split('.').pop().trim();
