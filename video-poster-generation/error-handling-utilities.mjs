import axios from 'axios';

// Error codes enum
export const ERROR_CODES = {
    INVALID_INPUT: 'INVALID_INPUT',
    DOWNLOAD_FAILED: 'DOWNLOAD_FAILED',
    VIDEO_GENERATION_FAILED: 'VIDEO_GENERATION_FAILED',
    UPLOAD_FAILED: 'UPLOAD_FAILED',
    THUMBNAIL_GENERATION_FAILED: 'THUMBNAIL_GENERATION_FAILED',
    API_CALL_FAILED: 'API_CALL_FAILED',
    NETWORK_ERROR: 'NETWORK_ERROR',
    PROCESS_TIMEOUT: 'PROCESS_TIMEOUT'
};

export const sendErrorCallback = async (job_id, errorCode, message, callback_url) => {
    try {
        // Skip callback if URL is a test URL or if we're in local testing mode
        if (callback_url.includes('example.com') || callback_url.includes('httpbin.org')) {
            console.log(`Skipping error callback for test URL: ${callback_url}`);
            console.log(`Error details - Job ID: ${job_id}, Code: ${errorCode}, Message: ${message}`);
            return;
        }

        await axios.put(`${callback_url}/generation-failed`, {
            job_id: job_id,
            error_code: errorCode,
            message: message
        });
    } catch (err) {
        console.error('Failed to send error callback:', err.message);
        // Don't throw the error, just log it to prevent cascading failures
    }
};

export const validateIncomingData = (event) => {
    const { elements, frame_width, frame_height, job_id, callback_url } = event;
    const payload = elements;
    // Validate required fields
    if (!payload || !frame_width || !frame_height || !job_id || !callback_url) {
        throw new Error(ERROR_CODES.INVALID_INPUT);
    }

    // Validate payload is an array
    if (!Array.isArray(payload)) {
        throw new Error(ERROR_CODES.INVALID_INPUT);
    }

    // Validate each media element in the payload
    payload.forEach((element, index) => {
        const { url, width, height, x, y, type } = element;

        if (!url || !width || !height || x === undefined || y === undefined || !type) {
            throw new Error(`${ERROR_CODES.INVALID_INPUT}: Missing required fields in payload element ${index}`);
        }

        if (!['photo', 'video'].includes(type)) {
            throw new Error(`${ERROR_CODES.INVALID_INPUT}: Invalid type '${type}' in payload element ${index}. Must be 'photo' or 'video'`);
        }

        // Validate numeric values
        if (typeof width !== 'number' || typeof height !== 'number' ||
            typeof x !== 'number' || typeof y !== 'number') {
            throw new Error(`${ERROR_CODES.INVALID_INPUT}: Width, height, x, and y must be numbers in payload element ${index}`);
        }

        // Validate optional aspect_ratio_mode parameter
        if (element.aspect_ratio_mode !== undefined) {
            if (typeof element.aspect_ratio_mode !== 'string') {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: aspect_ratio_mode must be a string in payload element ${index}`);
            }
            if (!['fill', 'preserve', 'original'].includes(element.aspect_ratio_mode)) {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: aspect_ratio_mode must be 'fill', 'preserve', or 'original' in payload element ${index}`);
            }
        }

        // Validate border properties for videos (all required for video elements)
        if (type === 'video') {
            // border object is required for videos
            if (!element.border || typeof element.border !== 'object') {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: border object is required for video elements in payload element ${index}`);
            }

            const { radius, width, color } = element.border;

            // radius is required for videos
            if (radius === undefined) {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.radius is required for video elements in payload element ${index}`);
            }
            if (typeof radius !== 'number') {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.radius must be a number in payload element ${index}`);
            }
            if (radius < 0) {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.radius must be non-negative in payload element ${index}`);
            }

            // width is required for videos
            if (width === undefined) {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.width is required for video elements in payload element ${index}`);
            }
            if (typeof width !== 'number') {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.width must be a number in payload element ${index}`);
            }
            if (width < 0) {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.width must be non-negative in payload element ${index}`);
            }

            // color is required for videos
            // if (color === undefined) {
            //     throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.color is required for video elements in payload element ${index}`);
            // }
            // if (typeof color !== 'string') {
            //     throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.color must be a string in payload element ${index}`);
            // }

            // // Validate color format (hex, rgb, or named color)
            // const colorPattern = /^(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)|rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)|[a-zA-Z]+)$/;
            // if (!colorPattern.test(color)) {
            //     throw new Error(`${ERROR_CODES.INVALID_INPUT}: border.color must be a valid color (hex, rgb, rgba, or named color) in payload element ${index}`);
            // }

            // Warn if border radius is too large (will be clamped during processing)
            const maxRadius = Math.floor(Math.min(width, height) / 2);
            if (radius > maxRadius) {
                console.warn(`Warning: border.radius ${radius} is too large for video dimensions ${width}x${height} in payload element ${index}. Will be clamped to ${maxRadius}.`);
            }
        } else {
            // For photos, border property should not be present
            if (element.border !== undefined) {
                throw new Error(`${ERROR_CODES.INVALID_INPUT}: border property can only be specified for video elements in payload element ${index}`);
            }
        }
    });

    // Validate frame dimensions
    if (typeof frame_width !== 'number' || typeof frame_height !== 'number') {
        throw new Error(`${ERROR_CODES.INVALID_INPUT}: frame_width and frame_height must be numbers`);
    }

    if (frame_width <= 0 || frame_height <= 0) {
        throw new Error(`${ERROR_CODES.INVALID_INPUT}: frame_width and frame_height must be positive numbers`);
    }
};
