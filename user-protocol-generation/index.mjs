import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import fs from 'fs';
import { join } from 'path';
import { tmpdir } from "os";
import puppeteer from 'puppeteer-core';
import chromium from '@sparticuz/chromium';

function delay(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}

const _folderName = 'production/user-protocol-photos';

export const handler = async (event) => {
    const { user_id, bearer_token } = event;

    if (!user_id || !bearer_token) {
        return {
            statusCode: 400,
            body: JSON.stringify({ message: 'Missing required parameters: user_id, bearer_token' }),
        };
    }

    console.log('Generating Protocol Photo for user_id:', user_id);

    let browser;

    try {
        // Launch the browser
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-web-security', ...chromium.args],
            executablePath: await chromium.executablePath(),
        });

        const page = await browser.newPage();
        await page.setViewport({ width: 630, height: 188, deviceScaleFactor: 2 });
        await page.setExtraHTTPHeaders({ Authorization: 'Bearer ' + bearer_token });

        const url = `https://pp.thecircleapp.in/#/protocol_page?user_id=${user_id}`;

        await page.goto(url, { waitUntil: 'networkidle0', timeout: 30000 });
        await page.waitForNetworkIdle({ idleTime: 2000, timeout: 30000 });
        await delay(2000); // Give some time for rendering

        // Take the screenshot
        const screenshotPath = join(tmpdir(), `protocol_photo_${user_id}.png`);
        await page.screenshot({ 
            path: screenshotPath,
            omitBackground: true,
            clip: { x: 0, y: 0, width: 630, height: 188 }
        });

        // Upload to S3
        const bucketName = 'circle-app-photos';
        const currentTimeStamp = new Date().getTime();
        const outputFileName = `protocol-${user_id}-${currentTimeStamp}.png`;
        await uploadPhotoToS3(screenshotPath, bucketName, outputFileName);

        return {
            statusCode: 200,
            body: JSON.stringify({
                protocol_url: `https://a-cdn.thecircleapp.in/${_folderName}/${outputFileName}`,
                message: 'Protocol photo generated successfully',
            }),
        };

    } catch (err) {
        console.error('Error in generating protocol photo:', err);
        if (browser) {
            await browser.close();
        }
        return {
            statusCode: 500,
            body: JSON.stringify({ message: 'Failed to generate protocol photo' }),
        };
    } finally {
        if (browser) {
            await browser.close();
        }
    }
};

export const uploadPhotoToS3 = async (filePath, bucketName, fileName) => {
    const s3 = new S3Client({ region: 'ap-south-1' });
    const uploadParams = {
        Bucket: bucketName,
        Key: `${_folderName}/${fileName}`,
        Body: fs.createReadStream(filePath),
    };

    try {
        await s3.send(new PutObjectCommand(uploadParams));
    } catch (err) {
        console.error('Error uploading to S3:', err);
        throw new Error(`Failed to upload protocol photo: ${err.message}`);
    }
};