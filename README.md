# P<PERSON><PERSON>

## Prerequisites
- [Node.js](https://nodejs.org/en/)
- [AWS CLI](https://aws.amazon.com/cli/)
- [AWS SAM CLI](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html) - `brew install aws-sam-cli`

## Setup
```bash
$ cd contacts-processor
$ npm i
$ sam build
```

## Local Invocation
Modify `payload.json` as needed(this is the input payload for the lambda)
```bash
export DOCKER_HOST=unix://$HOME/.docker/run/docker.sock
sam local invoke -e payload.json
```

## Deploy
```bash
$ sam deploy
```
