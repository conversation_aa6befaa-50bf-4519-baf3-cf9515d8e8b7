AWSTemplateFormatVersion: '2010-09-09'
Transform: 'AWS::Serverless-2016-10-31'
Resources:
  UserCampaignPosterGenerationPreprod:
    Type: 'AWS::Serverless::Function'
    Properties:
      Handler: index.handler
      Runtime: nodejs20.x
      CodeUri: ./
      MemorySize: 1024
      Timeout: 180
      Role: arn:aws:iam::666527360739:role/video-gen-lambda-role
      Events:
        PosterCampaignApi:
          Type: Api
          Properties:
            Path: /generate-campaign-poster-preprod
            Method: post

Outputs:
  UserPosterGenerationPreprodApi:
    Description: 'API Gateway endpoint URL for Prod stage for UserCampaignPosterGenerationPreprod function'
    Value:
      Fn::Sub: 'https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/generate-campaign-poster-preprod/'
