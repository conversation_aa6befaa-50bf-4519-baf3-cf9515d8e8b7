import Database from 'better-sqlite3';
import { createReadStream } from 'node:fs';
import { tmpdir } from 'node:os';
import { ulid } from 'ulid';
import { join } from 'node:path';
import csv from 'csv-parser';
import { hasRedWord, hasUnsubscribedWord, extractPratyName, extractRole } from './red-words.mjs';

const fixQuotedValues = (value) => {
	// remove quotes anywhere in the string
	return value.replace(/['"]+/g, '');
};

export const convertCSVToSQLite = async (filePath) => {

	const TABLE_NAME = 'contacts';

	const dbFilePath = join(tmpdir(), `${ulid()}.db`);
	// Create DB and table
	const db = new Database(dbFilePath);
	db.prepare(
		`CREATE TABLE ${TABLE_NAME} (` +
		'uploaded_phone_number INTEGER,' +
		'Name TEXT,' +
		'uploader_user_id INTEGER,' +
		'uploader_phone_number INTEGER,' +
		'has_red_word BOOLEAN DEFAULT false,' +
		'has_unsubscribed_word BOOLEAN DEFAULT false,' +
		'party_name TEXT DEFAULT NULL,' +
		'role TEXT DEFAULT NULL' +
		');'
	).run();

	const insert = (batch) => {
		const command = `INSERT INTO ${TABLE_NAME} (uploaded_phone_number, Name, uploader_user_id, uploader_phone_number, has_red_word, has_unsubscribed_word, party_name, role) VALUES ${batch.join(',')};`
		try {
			db.exec(command);
		} catch (e) {
			console.error(`[Error] ${e.message}`);
			console.error(`[Command] ${command}`);
			throw e;
		}
	};

	const BATCH_SIZE = 10000;
	let batch = [];

	return new Promise((resolve, reject) => {
		createReadStream(filePath)
			.pipe(csv())
			.on('data', (data) => {
				try {
					const phone_number = data['uploaded_phone_number'];
					// check if first digit is > 5
					if (phone_number.toString().charAt(0) <= 5) {
						return;
					}

					const name = data['Name'];
					const partyName = extractPratyName(name);
					const role = extractRole(name);

					const values = [
						phone_number,
						`'${fixQuotedValues(name)}'`,
						data['uploader_user_id'],
						data['uploader_phone_number'],
						hasRedWord(name),
						hasUnsubscribedWord(name),
						partyName ? `'${partyName}'` : 'NULL',
						role ? `'${role}'` : 'NULL',
					];
					batch.push(`(${values.join(',')})`);
					if (batch.length >= BATCH_SIZE) {
						insert(batch);
						batch = [];
					}
				} catch (e) {
					reject(e);
				}
			})
			.on('end', () => {
				if (batch.length > 0) {
					insert(batch);
				}
				resolve(db);
			});
	});
}
