import { convertCSVToSQLite } from './csv-to-sqlite.mjs';
import { downloadCSVFile } from './download-csv.mjs';
import fs from 'node:fs';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { writeToCSV } from './write-to-csv.mjs';

async function uploadCSVFile(bucket, key, filePath) {
	const s3 = new S3Client({ region: 'ap-south-1' });
	const fileStream = fs.createReadStream(filePath);
	const uploadParams = {
		Bucket: bucket,
		Key: key,
		Body: fileStream,
	};
	await s3.send(new PutObjectCommand(uploadParams));
}

export const handler = async (event, _) => {
	const bucket = event.bucket || 'praja-raw-contacts-data';
	const key = event.key;
	const outBucket = event.outBucket || 'praja-processed-contacts-data';
	const outKey = event.outKey || `processed-${key}`;
	console.log(`Downloading ${bucket}/${key}`);
	const inputFilePath = await downloadCSVFile(bucket, key);
	console.log(`Downloaded.Converting to SQLite`);
	const db = await convertCSVToSQLite(inputFilePath);
	console.log('CSV file successfully processed');
	// delete the input file
	await fs.promises.unlink(inputFilePath);

	db.prepare('CREATE INDEX contacts_uploaded_phone_number ON contacts(uploaded_phone_number);').run();
	db.prepare('CREATE TEMP VIEW density AS SELECT uploaded_phone_number, COUNT(*) AS count FROM contacts GROUP BY uploaded_phone_number;').run();
	db.prepare(`CREATE TEMP VIEW roles_count AS SELECT uploaded_phone_number, role, COUNT(*) AS count FROM contacts WHERE role IS NOT NULL GROUP BY uploaded_phone_number, role;`).run();
	db.prepare(`CREATE TEMP VIEW roles AS SELECT r1.uploaded_phone_number, r1.role 
FROM roles_count r1 INNER JOIN 
(SELECT uploaded_phone_number, MAX(count) AS count from roles_count GROUP BY uploaded_phone_number) r2 
ON r1.uploaded_phone_number = r2.uploaded_phone_number AND r1.count = r2.count`).run();
	db.prepare(`CREATE TEMP VIEW parties_count AS SELECT uploaded_phone_number, party_name, COUNT(*) AS count FROM contacts WHERE party_name IS NOT NULL GROUP BY uploaded_phone_number, party_name;`).run();
	db.prepare(`CREATE TEMP VIEW parties AS SELECT p1.uploaded_phone_number, p1.party_name, p2.party_count
FROM parties_count p1 INNER JOIN
(SELECT uploaded_phone_number, COUNT(*) as party_count, MAX(count) AS count from parties_count GROUP BY uploaded_phone_number) p2
ON p1.uploaded_phone_number = p2.uploaded_phone_number AND p1.count = p2.count`).run();
	db.prepare(`CREATE TEMP VIEW unsubscribed AS SELECT uploaded_phone_number, COUNT(*) AS unsub_count FROM contacts WHERE has_unsubscribed_word = 1 GROUP BY uploaded_phone_number;`).run();

	const stmt = db.prepare(
		`SELECT parties.uploaded_phone_number, Name, count, parties.party_name, parties.party_count, roles.role, contacts.has_red_word
FROM parties 
LEFT JOIN density ON parties.uploaded_phone_number = density.uploaded_phone_number 
LEFT JOIN roles ON parties.uploaded_phone_number = roles.uploaded_phone_number 
LEFT JOIN unsubscribed ON parties.uploaded_phone_number = unsubscribed.uploaded_phone_number 
INNER JOIN contacts ON parties.uploaded_phone_number = contacts.uploaded_phone_number AND contacts.has_unsubscribed_word = 0 AND contacts.party_name = parties.party_name
WHERE unsubscribed.unsub_count IS NULL
GROUP BY parties.uploaded_phone_number
ORDER BY count DESC LIMIT 10000;`
	);
	const outputPath = await writeToCSV(stmt);
	console.log(`Uploading ${outBucket}/${outKey}`);
	await uploadCSVFile(outBucket, outKey, outputPath);
	console.log('Uploaded');
	// delete the output file
	await fs.promises.unlink(outputPath);
	const dbFilePath = db.name;
	db.close();
	// delete the db file
	await fs.promises.unlink(dbFilePath);
	return {
		statusCode: 200,
	}
}
