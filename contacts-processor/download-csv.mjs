import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { tmpdir } from 'node:os';
import { ulid } from 'ulid';
import fs from 'node:fs';
import { join } from 'node:path';

const s3 = new S3Client({ region: 'ap-south-1' });

export async function downloadCSVFile(bucket, key) {
	const getObjectCommand = new GetObjectCommand({
		Bucket: bucket,
		Key: key,
	});

	const response = await s3.send(getObjectCommand);
	const downloadPath = join(tmpdir(), `${ulid()}.csv`);
	const writer = fs.createWriteStream(downloadPath);
	const downloadStream = response.Body;
	if (downloadStream) {
		downloadStream.pipe(writer);
		await new Promise((resolve, reject) => {
			writer.on('finish', () => resolve(downloadPath));
			writer.on('error', reject);
		});
		return downloadPath;
	} else {
		throw new Error(`Unable to download the file s3::${bucket}/${key}`);
	}
}
