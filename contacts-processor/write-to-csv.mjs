import { tmpdir } from 'node:os';
import { ulid } from 'ulid';
import { join } from 'node:path';
import { createObjectCsvWriter } from 'csv-writer';

export async function writeToCSV(stmt) {
	const csvPath = join(tmpdir(), `${ulid()}.csv`);
	const csvWriter = createObjectCsvWriter({
		path: csvPath,
		header: [
			{ id: 'uploaded_phone_number', title: 'phone_number' },
			{ id: 'Name', title: 'name' },
			{ id: 'count', title: 'network_density' },
			{ id: 'party_name', title: 'party' },
			{ id: 'party_count', title: 'party_count' },
			{ id: 'role', title: 'role' },
		],
	});
	const BATCH_SIZE = 10000;
	let batch = [];
	for (const row of stmt.iterate()) {
		row.Name = row.has_red_word ? '' : row.Name;
		// remove new lines in name
		row.Name = row.Name.replace(/\n/g, '');
		row.Name = row.Name.replace(/\r/g, '');
		batch.push(row);
		if (batch.length >= BATCH_SIZE) {
			await csvWriter.writeRecords(batch);
			batch = [];
		}
	}
	if (batch.length > 0) {
		await csvWriter.writeRecords(batch);
	}
	return csvPath;
}
