const redWords = [
	"2nd setup",
	"2వ సెటప్",
	"aathu",
	"akka",
	"ammadu",
	"ammanidenga",
	"ammulu",
	"angel",
	"ann",
	"any qualification",
	"arripuka",
	"asshole",
	"astrology",
	"atha",
	"athayya",
	"atm",
	"aunty",
	"auto",
	"babe",
	"baby",
	"bad boy",
	"badco",
	"bali",
	"balli",
	"bamardi",
	"bangaram",
	"bangaru",
	"bastard",
	"batta",
	"bava",
	"bava garu",
	"bavaa",
	"bavagaru",
	"bewarse",
	"bhenchod",
	"bichagadu",
	"bike",
	"bits",
	"black boy",
	"bocchu",
	"bochhu",
	"bochu",
	"bochuga",
	"bokka",
	"boku party",
	"bonda",
	"bondha",
	"bongu",
	"boobs",
	"bore",
	"bosdike",
	"bosedk",
	"bosudeke",
	"brand",
	"bro",
	"broker",
	"bujji",
	"bulley",
	"bulli",
	"buruga",
	"button reddy",
	"call girl",
	"call me",
	"candy",
	"chakka",
	"chekka",
	"chelli",
	"chetta yedava",
	"chinni",
	"chukka",
	"chutiyee",
	"color",
	"colour",
	"crooks",
	"current",
	"dad",
	"dada",
	"darling",
	"dayyam",
	"denga",
	"dengey",
	"dengichuko",
	"dengichukoo",
	"denguthaa",
	"dhaka",
	"dkbose",
	"doctor",
	"dog",
	"don",
	"donga",
	"drink",
	"drinker",
	"driver",
	"dunna",
	"edava",
	"erri",
	"errilanjaa",
	"erripooka",
	"erripuk",
	"exide",
	"fcuk",
	"flats",
	"fuck",
	"fuck u",
	"fuck you",
	"fuckmee",
	"fuckyou",
	"gadu",
	"gajala",
	"gali",
	"galodu",
	"god",
	"home",
	"hookah",
	"horoscope",
	"hot",
	"hotaunty",
	"hotty",
	"house",
	"howla badcow",
	"husband",
	"images",
	"jaffa",
	"jesus",
	"job vacancy",
	"jyothishyam",
	"kaki",
	"kanna",
	"karri",
	"karroda",
	"kasak",
	"kathi",
	"kedi",
	"keedi",
	"khilli",
	"killi",
	"king",
	"kirana",
	"kojja",
	"konda",
	"kukka",
	"kutha",
	"kuthanuuu",
	"ladda",
	"laddu",
	"lafoot",
	"lambadi",
	"lambidikodaka",
	"lanja",
	"lanjaa",
	"lanjaaa",
	"lanjakoduku",
	"lavada",
	"lavda",
	"lavdalonaasulli",
	"lawada",
	"life changing opportunity",
	"lingam",
	"lofar congress",
	"lorry",
	"lovda",
	"love",
	"lover",
	"lover boy",
	"lowda",
	"lowde",
	"luccha",
	"luka",
	"ma",
	"macanic",
	"machanic",
	"mad",
	"maddaa",
	"makka",
	"mama",
	"mandhu",
	"mangalodu",
	"massage",
	"matharchod",
	"mava",
	"mavayya",
	"mawa",
	"mawo",
	"menthol",
	"milk",
	"mill",
	"modaaa",
	"modda",
	"moddaa",
	"moddaga",
	"mogudu",
	"mom",
	"mopi",
	"mother",
	"motherfucker",
	"mukka",
	"munda",
	"munda mopi",
	"mundamopi",
	"munddaa",
	"my dear",
	"my love",
	"my world",
	"na batta",
	"nabatta",
	"naik",
	"nakka",
	"nalla",
	"nalla balu",
	"nayak",
	"neeyammanadenga",
	"no boss no targets",
	"no boss no targets intersted by any person",
	"office",
	"office boy",
	"online digital work from home",
	"paint",
	"pala",
	"palu",
	"pamba",
	"pandi",
	"pappu",
	"part time- full time",
	"paytm",
	"pedarayudu wife",
	"pellam",
	"photo",
	"phuppu",
	"picchinakodaka",
	"pichayya",
	"pichi",
	"pichipuka",
	"pichodi",
	"pilla",
	"pilli",
	"pinni",
	"pokiri",
	"pokodi",
	"pokodigadu",
	"pop",
	"poramboku",
	"postman",
	"pottada",
	"potti",
	"prema",
	"prick",
	"puka",
	"pukka",
	"puku",
	"pukuu",
	"pulaka",
	"puppa",
	"pussy",
	"ranku",
	"rankumunda",
	"red",
	"red puka",
	"red puku",
	"rowdy",
	"sachinodu",
	"sala",
	"sale",
	"salee",
	"sand",
	"sannasi",
	"sc",
	"sentring",
	"setup",
	"sex",
	"shedgadu",
	"shop",
	"siddy",
	"sister",
	"sodigadu",
	"sollu",
	"sollugadu",
	"solugadu",
	"spa",
	"st",
	"sulla",
	"sullakodaka",
	"sulli",
	"sulliga",
	"tele caller work",
	"thalli",
	"thief",
	"thinga",
	"thingari",
	"thoofan",
	"torchar",
	"training",
	"transport",
	"truck",
	"tyres",
	"vada",
	"vatta",
	"vattakayalu",
	"voda",
	"waste",
	"waste fellow",
	"water",
	"white",
	"wife",
	"work from home",
	"work from home jobs opportunity",
	"work from home opportunity",
	"worker",
	"workfromhome",
	"xerox",
	"xxxxx",
	"yammana",
	"yedava",
	"yerripuku",
	"అంగడి",
	"అక్క",
	"అక్కా",
	"అత్త",
	"అత్తయ్య",
	"అమ్మ",
	"అమ్మకం",
	"అమ్మడు",
	"అమ్మనిదెంగా",
	"అమ్ములు",
	"అర్రిపుక",
	"అర్రిపుకు",
	"అవినీతి సీఎం",
	"ఆంటీ",
	"ఆంటీక్స్",
	"ఆతు",
	"ఆదాయాన్ని సంపాదించే మార్గం",
	"ఆదాయాన్ని సంపాదించే మార్గం.",
	"ఆఫీసు బాయ్",
	"ఆర్",
	"ఆసక్తిగల అభ్యర్థుల కోసం వెతుకుతున్నాము.",
	"ఆస్ట్రాలజీ",
	"ఇంటి",
	"ఇంటి నుండి పనిచేయండి",
	"ఇంట్లో ఉండి పని చేసే అవకాశం",
	"ఇల్లు",
	"ఇసుక",
	"ఎదవ",
	"ఎదవా",
	"ఎరుపు",
	"ఎరుపు పుకా",
	"ఎరుపు పుకు",
	"ఎర్రి",
	"ఎర్రిపుక్",
	"ఎర్రిపూకా",
	"ఎర్రిలంజా",
	"ఎస్ టి",
	"ఎస్సీ",
	"ఏటీఎం",
	"ఐస్ ఫ్రూట్",
	"ఒరేయ్ పచ్చబ్యాచ్చినాకొడకల్లారా",
	"ఓరీ లుచ నువు ఎన్ని డ్రామాలు చేసిస్నిన కూడా నిన్ను ఆంధ్రా నుండి తెలంగాణ రాష్ట్ర జైల్ కీ తరలించడానికి సిద్ధం గా ఉన్నారు",
	"కత్తి",
	"కన్న",
	"కర్రీ",
	"కర్రోడ",
	"కసక్",
	"కాకి",
	"కార్మికుడు",
	"కార్యాలయం",
	"కిరణ షాప్",
	"కిల్లి",
	"కీడి",
	"కుక్క",
	"కుక్కపిల్ల",
	"కుత",
	"కుత్త",
	"కుత్తను",
	"కేడి",
	"కొండా",
	"కొజ్జా",
	"క్రో జిరాక్స్",
	"ఖిల్లి",
	"గజల",
	"గల్లీ",
	"గాడిద",
	"గాడు",
	"గాలి",
	"గాలోడు",
	"గుచ్చు",
	"గుద్ద",
	"చక్కా",
	"చలిముండ",
	"చాకలోడు",
	"చిత్రాలు",
	"చిన్ని",
	"చుక్క",
	"చుటీయీ",
	"చుతియా",
	"చెక్కా",
	"చెడ్డ యెదవ",
	"చెల్లి",
	"జాగ్రత్త",
	"జాఫా",
	"జిరాక్స్",
	"జ్యోతిషం",
	"జ్యోతిష్యం",
	"టాప్",
	"టార్చర్",
	"టైర్లు",
	"ట్రక్",
	"ట్రె న్యూ",
	"డాన్",
	"డార్లింగ్",
	"డికె బోస్",
	"డెంగిచుకో",
	"డెంగీ",
	"డెంగుతా",
	"డ్రైవర్",
	"ఢాకా",
	"తక్కువ",
	"తల్లి",
	"తాగుబోతు",
	"తింగరీ",
	"తూఫాన్",
	"తెలుపు",
	"త్రాగండి",
	"దయ్యం",
	"దాదా",
	"దుకాణం",
	"దున్న",
	"దుర్మార్గుడు",
	"దెంగు",
	"దెంగెచుకో",
	"దెంగేయ్",
	"దేన్గించుకో",
	"దేవదూత",
	"దేవుడు",
	"దొంగ",
	"నక్క",
	"నల్ల",
	"నల్ల కుర్రాడు",
	"నల్ల బాలు",
	"నా ప్రపంచం",
	"నా ప్రియతమా",
	"నా ప్రియమైన",
	"నా బట్ట",
	"నాన్న",
	"నాయక్",
	"నిరుద్యోగులకు ప్రముఖ కంపెనీ లో లీడర్ గా పని చేసే అవకాశం",
	"నిష్క్రమించు",
	"నీ అమ్మను దెంగా",
	"నీఅమ్మనుదెంగా",
	"నీటి",
	"నీయమ్మ",
	"నెలకి *20k to 30k  ఆదాయాన్ని సంపాదించే మార్గం",
	"నెలకి *20k to 30k 🤩ఆదాయాన్ని సంపాదించే మార్గం",
	"పంది",
	"పంబ",
	"పక్కా",
	"పాప్",
	"పాల",
	"పాలు",
	"పాలూ",
	"పిచ్చి",
	"పిచ్చిపుక",
	"పిచ్చోడు",
	"పిన్ని",
	"పిల్లా",
	"పిల్లి",
	"పిహెచ్ దొంగ",
	"పుక",
	"పుకు",
	"పుకుయు",
	"పులక",
	"పుస్సీ",
	"పూకా",
	"పెటియం",
	"పెట్టు",
	"పెదరాయుడు",
	"పెద్దముండ",
	"పెద్దలంజ",
	"పెయింట్",
	"పెల్లం",
	"పై",
	"పొట్టాడ",
	"పొట్టి",
	"పొర పుల్కా",
	"పోకిరి",
	"పోకొడిగాడు",
	"పోకోడి",
	"పోరంబోకు",
	"పోస్ట్‌మ్యాన్",
	"ప్చయ్య",
	"ప్రముఖ కంపెనీ లో లీడర్ గా పని చేసే అవకాశం",
	"ప్రస్తుత",
	"ప్రియతమా",
	"ప్రేమ",
	"ప్రేమికుడు",
	"ఫక్",
	"ఫక్ ఆఫ్",
	"ఫక్మీ",
	"ఫక్యు",
	"ఫుప్పు",
	"ఫోటో",
	"ఫోన్ ద్వారా..రోజు 3 to 4 గంటలు🕰️పని చేస్తూ... నెలకి *20k to 30k 🤩ఆదాయాన్ని సంపాదించే మార్గం.",
	"ఫ్రెండ్",
	"ఫ్లాట్",
	"ఫ్లాట్లు",
	"బంగారం",
	"బంగారు",
	"బండి",
	"బట్ట",
	"బల్లి",
	"బాడ్ బాయ్",
	"బాలి",
	"బావ",
	"బావ గారు",
	"బావగారు",
	"బాస్",
	"బిచ్చగాడు",
	"బిట్స్",
	"బీచ్",
	"బీరువా",
	"బుజ్జి",
	"బుల్లి",
	"బూరుగ",
	"బెంచోడ్",
	"బేబీ",
	"బైక్",
	"బొంగు",
	"బొక్క",
	"బొచ్చు",
	"బొచ్చుగా",
	"బోండా",
	"బోర్",
	"బోసిడికే",
	"బోసుడేకే",
	"బోస్డైక్",
	"బ్రాండ్",
	"బ్రో",
	"బ్రోకర్లు",
	"భర్త",
	"భార్య",
	"మంగళోడు",
	"మందు",
	"మక్కా",
	"మడ్డ",
	"మదర్ఫకర్",
	"మద్దా",
	"మధ్యవర్తి",
	"మరిది",
	"మసాజ్",
	"మాదర్చోద్",
	"మాదిగ",
	"మామా",
	"మాల",
	"మావయ్య",
	"మావా",
	"మావో",
	"మిఠాయి",
	"మిల్",
	"ముండ",
	"ముండమోపి",
	"ముండా",
	"ముక్కా",
	"మెంథాల్",
	"మెకానిక్",
	"మొగుడు",
	"మొడ్డ",
	"మొడ్డగా",
	"మోడ",
	"మోడీ మోడ్డ గుడవనిధే ఎవ్వరి కి తెల్లవార దు",
	"మోడ్డ",
	"మోడ్డగా",
	"మోడ్డా",
	"మోపి",
	"మోవో",
	"యువతీ యువకులు, నిరుద్యోగులు కావలెను",
	"యెడవా",
	"యేసు",
	"రంకు",
	"రంకుముండ",
	"రంగు",
	"రవాణా",
	"రోడ్డు",
	"రౌడీ",
	"లం జా కొడకా",
	"లం జా కొడుకు",
	"లంగా గాడు",
	"లంజ",
	"లంజకొడక",
	"లంజకొడుకు",
	"లంజముండ",
	"లంజా",
	"లంజాకొడుకు",
	"లంబాడీ",
	"లంబిడికొడకా",
	"లడ్డా",
	"లడ్డూ",
	"లఫూట్",
	"లవడా",
	"లవర్ బాయ్",
	"లాంజ",
	"లాడా",
	"లారీ",
	"లింగం",
	"లూకా",
	"లోఫర్ నా కొడకా.. మంచి ఎక్స్పీరియన్స్ సీఎం కదరా పచ్చకామెర్లు పచ్చకామె నా కొడకా",
	"వక్షోజాలు",
	"వట్ట",
	"వట్టకాయల",
	"వట్టకాయలు",
	"వడ",
	"విక్రయదారుడు",
	"విషయం",
	"వెదవ",
	"వైద్యుడు",
	"వేడి",
	"వేడిగా",
	"వేశ్య",
	"వేస్ట్",
	"వేస్ట్ ఫెలో",
	"వోడా",
	"వ్యర్థం",
	"శిక్షణ",
	"శిశువు",
	"షెడ్గాడు",
	"సచ్చినోడు",
	"సన్నాసి",
	"సాలా",
	"సిద్ది",
	"సుల్ల",
	"సుల్లకొడకా",
	"సుల్లి",
	"సుల్లిగ",
	"సుల్లిగా",
	"సెక్స్",
	"సెటప్",
	"సొలుగాడు",
	"సొల్లు",
	"సొల్లుగాడు",
	"సోడిగాడు",
	"సోదరి",
	"స్పా",
	"స్వచ్ఛందంగా",
	"హుక్కా",
	"హోటౌంటీ",]

export function hasRedWord(name) {
	const nameWords = name.toLowerCase().split(/\W+/);
	return nameWords.some(word => redWords.includes(word));
}

const unsubscribedWords = "bank|helpline|care|corona|gas|police|si|ci|dsp|customer|loan|current|power|enquiry|cook|insurance|lic|emergency|alert|word|help|elec|electricity|cable|dish|ttv|bike|pani|dress|cyber|tailor|jio|airtel|milk|mestry|hdfc|sbi|card|properties|driver|solar|auto|mutton|chicken|shop|addhar|aadhar|adhar|ambulance|car|diver|saloon|salon|brokar|broker|mech|mechanic|xerox|studio|designer|design|hotel|cutting|tank|cleaning|clean|clining|barbar|marriage|marrage|berow|sms";

export function hasUnsubscribedWord(name) {
	const regex = new RegExp(`\\b(${unsubscribedWords})\\b`, 'i');
	return regex.test(name);
}

const parties = {
	'YSRCP': 'YSRCP',
	'YCP': 'YSRCP',
	'TDP': 'TDP',
	'BJP': 'BJP',
	'CONGRESS': 'INC',
	'INC': 'INC',
	'JANASENA': 'JSP',
	'JSP': 'JSP',
	'JANASAINIK': 'JSP',
	'JANASAINIK': 'JSP',
	'BRS': 'BRS',
	'TRS': 'BRS',
}

export function extractPratyName(name) {
	const keys = Object.keys(parties);
	const nameWords = name.split(/\W+/);
	const partyKey = nameWords.find(word => keys.includes(word.toUpperCase()));
	if (partyKey) {
		return parties[partyKey.toUpperCase()];
	}
	return null;
}

const roles = {
	"MPP": "MPP",
	"ZPTC": "ZPTC",
	"CORPORATOR": "CORPORATOR",
	"LEADER": "LEADER",
	"SARPANCH": "SARPANCH",
	"కార్పొరేటర్": "CORPORATOR",
	"సర్పంచ్": "SARPANCH",
}

export function extractRole(name) {
	const keys = Object.keys(roles);
	const nameWords = name.split(/[ .\-,\(\)]+/);
	const roleKey = nameWords.find(word => keys.includes(word.toUpperCase()));
	if (roleKey) {
		return roles[roleKey.toUpperCase()];
	}
	return null;
}

